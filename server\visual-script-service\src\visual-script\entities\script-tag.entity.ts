/**
 * script-tag.entity.ts
 * 
 * 脚本标签实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique
} from 'typeorm';
import { VisualScript } from './visual-script.entity';

/**
 * 标签类型枚举
 */
export enum TagType {
  CATEGORY = 'category',
  FEATURE = 'feature',
  DIFFICULTY = 'difficulty',
  PLATFORM = 'platform',
  CUSTOM = 'custom'
}

/**
 * 脚本标签实体
 */
@Entity('script_tags')
@Unique(['scriptId', 'name'])
@Index(['name', 'type'])
@Index(['scriptId'])
@Index(['createdAt'])
export class ScriptTag {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'script_id' })
  @Index()
  scriptId: string;

  @Column({ length: 50 })
  @Index()
  name: string;

  @Column({
    type: 'enum',
    enum: TagType,
    default: TagType.CUSTOM
  })
  type: TagType;

  @Column({ length: 7, nullable: true })
  color: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'created_by' })
  createdBy: string;

  @Column({ name: 'created_by_name', length: 100 })
  createdByName: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => VisualScript, script => script.scriptTags, {
    onDelete: 'CASCADE'
  })
  @JoinColumn({ name: 'script_id' })
  script: VisualScript;

  // 计算属性
  get isCategory(): boolean {
    return this.type === TagType.CATEGORY;
  }

  get isFeature(): boolean {
    return this.type === TagType.FEATURE;
  }

  get isDifficulty(): boolean {
    return this.type === TagType.DIFFICULTY;
  }

  get isPlatform(): boolean {
    return this.type === TagType.PLATFORM;
  }

  get isCustom(): boolean {
    return this.type === TagType.CUSTOM;
  }

  get displayColor(): string {
    if (this.color) {
      return this.color;
    }

    // 根据类型返回默认颜色
    const defaultColors = {
      [TagType.CATEGORY]: '#1890ff',
      [TagType.FEATURE]: '#52c41a',
      [TagType.DIFFICULTY]: '#fa8c16',
      [TagType.PLATFORM]: '#722ed1',
      [TagType.CUSTOM]: '#8c8c8c'
    };

    return defaultColors[this.type] || '#8c8c8c';
  }

  get typeDisplayName(): string {
    const typeNames = {
      [TagType.CATEGORY]: '分类',
      [TagType.FEATURE]: '功能',
      [TagType.DIFFICULTY]: '难度',
      [TagType.PLATFORM]: '平台',
      [TagType.CUSTOM]: '自定义'
    };
    return typeNames[this.type] || '未知';
  }

  // 辅助方法
  updateColor(color: string): void {
    // 验证颜色格式（简单的十六进制颜色验证）
    if (/^#[0-9A-F]{6}$/i.test(color)) {
      this.color = color;
    }
  }

  updateDescription(description: string): void {
    this.description = description;
  }

  /**
   * 生成基于名称的默认颜色
   * @returns 十六进制颜色值
   */
  generateDefaultColor(): string {
    // 基于标签名称生成一致的颜色
    let hash = 0;
    for (let i = 0; i < this.name.length; i++) {
      hash = this.name.charCodeAt(i) + ((hash << 5) - hash);
    }

    // 转换为十六进制颜色
    const color = Math.abs(hash).toString(16).substring(0, 6);
    return '#' + '000000'.substring(0, 6 - color.length) + color;
  }

  /**
   * 检查标签名称是否有效
   * @param name 标签名称
   * @returns 是否有效
   */
  static isValidName(name: string): boolean {
    // 标签名称规则：1-50个字符，只允许字母、数字、中文、连字符和下划线
    return /^[\w\u4e00-\u9fa5-]{1,50}$/.test(name);
  }

  /**
   * 检查颜色值是否有效
   * @param color 颜色值
   * @returns 是否有效
   */
  static isValidColor(color: string): boolean {
    return /^#[0-9A-F]{6}$/i.test(color);
  }

  /**
   * 获取预定义的标签建议
   * @param type 标签类型
   * @returns 标签建议列表
   */
  static getTagSuggestions(type: TagType): string[] {
    const suggestions = {
      [TagType.CATEGORY]: [
        '游戏逻辑', '用户界面', '数据处理', '网络通信', '文件操作',
        '图像处理', '音频处理', '动画效果', '物理模拟', '人工智能'
      ],
      [TagType.FEATURE]: [
        '拖拽', '点击', '键盘输入', '鼠标交互', '触摸手势',
        '数据绑定', '状态管理', '事件处理', '定时器', '循环'
      ],
      [TagType.DIFFICULTY]: [
        '初级', '中级', '高级', '专家', '大师'
      ],
      [TagType.PLATFORM]: [
        'Web', 'Mobile', 'Desktop', 'VR', 'AR',
        'iOS', 'Android', 'Windows', 'macOS', 'Linux'
      ],
      [TagType.CUSTOM]: []
    };

    return suggestions[type] || [];
  }

  /**
   * 创建标准化的标签名称
   * @param name 原始名称
   * @returns 标准化名称
   */
  static normalizeName(name: string): string {
    return name.trim().toLowerCase().replace(/\s+/g, '-');
  }

  toJSON(): any {
    return {
      id: this.id,
      scriptId: this.scriptId,
      name: this.name,
      type: this.type,
      typeDisplayName: this.typeDisplayName,
      color: this.color,
      displayColor: this.displayColor,
      description: this.description,
      createdBy: this.createdBy,
      createdByName: this.createdByName,
      createdAt: this.createdAt
    };
  }
}
