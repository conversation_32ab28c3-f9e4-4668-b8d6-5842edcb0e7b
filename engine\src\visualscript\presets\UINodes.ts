/**
 * UINodes.ts
 * 
 * UI相关的视觉脚本节点
 */

import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

/**
 * 创建按钮节点
 */
export class CreateButtonNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('text', 'string', '按钮文本', '按钮');
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('size', 'vector2', '大小', { x: 100, y: 40 });
    this.addInputSlot('style', 'object', '样式', {});
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('onClick', 'flow', '点击事件');
    this.addOutputSlot('button', 'object', '按钮对象');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const text = this.getInputValue('text') as string;
    const position = this.getInputValue('position') as { x: number; y: number };
    const size = this.getInputValue('size') as { x: number; y: number };
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建按钮
      const button = (uiSystem as any).createButton({
        text,
        position,
        size,
        style,
        onClick: () => {
          // 触发点击事件
          this.triggerFlow('onClick');
        }
      });

      // 设置输出值
      this.setOutputValue('button', button);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建按钮失败:', error);
      return false;
    }
  }
}

/**
 * 创建文本节点
 */
export class CreateTextNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('text', 'string', '文本内容', '文本');
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('fontSize', 'number', '字体大小', 16);
    this.addInputSlot('color', 'string', '颜色', '#000000');
    this.addInputSlot('style', 'object', '样式', {});
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('textElement', 'object', '文本元素');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const text = this.getInputValue('text') as string;
    const position = this.getInputValue('position') as { x: number; y: number };
    const fontSize = this.getInputValue('fontSize') as number;
    const color = this.getInputValue('color') as string;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建文本元素
      const textElement = (uiSystem as any).createText({
        text,
        position,
        fontSize,
        color,
        style
      });

      // 设置输出值
      this.setOutputValue('textElement', textElement);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建文本失败:', error);
      return false;
    }
  }
}

/**
 * 创建输入框节点
 */
export class CreateInputNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('placeholder', 'string', '占位符', '请输入...');
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('size', 'vector2', '大小', { x: 200, y: 30 });
    this.addInputSlot('inputType', 'string', '输入类型', 'text');
    this.addInputSlot('style', 'object', '样式', {});
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('onChange', 'flow', '值改变');
    this.addOutputSlot('onFocus', 'flow', '获得焦点');
    this.addOutputSlot('onBlur', 'flow', '失去焦点');
    this.addOutputSlot('inputElement', 'object', '输入框元素');
    this.addOutputSlot('value', 'string', '当前值');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const placeholder = this.getInputValue('placeholder') as string;
    const position = this.getInputValue('position') as { x: number; y: number };
    const size = this.getInputValue('size') as { x: number; y: number };
    const inputType = this.getInputValue('inputType') as string;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建输入框
      const inputElement = (uiSystem as any).createInput({
        placeholder,
        position,
        size,
        type: inputType,
        style,
        onChange: (value: string) => {
          this.setOutputValue('value', value);
          this.triggerFlow('onChange');
        },
        onFocus: () => {
          this.triggerFlow('onFocus');
        },
        onBlur: () => {
          this.triggerFlow('onBlur');
        }
      });

      // 设置输出值
      this.setOutputValue('inputElement', inputElement);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建输入框失败:', error);
      return false;
    }
  }
}

/**
 * 创建滑块节点
 */
export class CreateSliderNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('min', 'number', '最小值', 0);
    this.addInputSlot('max', 'number', '最大值', 100);
    this.addInputSlot('value', 'number', '初始值', 50);
    this.addInputSlot('step', 'number', '步长', 1);
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('size', 'vector2', '大小', { x: 200, y: 20 });
    this.addInputSlot('style', 'object', '样式', {});
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('onChange', 'flow', '值改变');
    this.addOutputSlot('sliderElement', 'object', '滑块元素');
    this.addOutputSlot('currentValue', 'number', '当前值');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const min = this.getInputValue('min') as number;
    const max = this.getInputValue('max') as number;
    const value = this.getInputValue('value') as number;
    const step = this.getInputValue('step') as number;
    const position = this.getInputValue('position') as { x: number; y: number };
    const size = this.getInputValue('size') as { x: number; y: number };
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建滑块
      const sliderElement = (uiSystem as any).createSlider({
        min,
        max,
        value,
        step,
        position,
        size,
        style,
        onChange: (newValue: number) => {
          this.setOutputValue('currentValue', newValue);
          this.triggerFlow('onChange');
        }
      });

      // 设置输出值
      this.setOutputValue('sliderElement', sliderElement);
      this.setOutputValue('currentValue', value);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建滑块失败:', error);
      return false;
    }
  }
}

/**
 * 创建图像节点
 */
export class CreateImageNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('src', 'string', '图像源', '');
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('size', 'vector2', '大小', { x: 100, y: 100 });
    this.addInputSlot('alt', 'string', '替代文本', '');
    this.addInputSlot('style', 'object', '样式', {});

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('onLoad', 'flow', '加载完成');
    this.addOutputSlot('onError', 'flow', '加载错误');
    this.addOutputSlot('imageElement', 'object', '图像元素');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const src = this.getInputValue('src') as string;
    const position = this.getInputValue('position') as { x: number; y: number };
    const size = this.getInputValue('size') as { x: number; y: number };
    const alt = this.getInputValue('alt') as string;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建图像
      const imageElement = (uiSystem as any).createImage({
        src,
        position,
        size,
        alt,
        style,
        onLoad: () => {
          this.triggerFlow('onLoad');
        },
        onError: () => {
          this.triggerFlow('onError');
        }
      });

      // 设置输出值
      this.setOutputValue('imageElement', imageElement);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建图像失败:', error);
      return false;
    }
  }
}

/**
 * 创建面板节点
 */
export class CreatePanelNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('size', 'vector2', '大小', { x: 300, y: 200 });
    this.addInputSlot('title', 'string', '标题', '面板');
    this.addInputSlot('resizable', 'boolean', '可调整大小', true);
    this.addInputSlot('draggable', 'boolean', '可拖拽', true);
    this.addInputSlot('style', 'object', '样式', {});

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('onClose', 'flow', '关闭事件');
    this.addOutputSlot('onResize', 'flow', '调整大小');
    this.addOutputSlot('panelElement', 'object', '面板元素');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const position = this.getInputValue('position') as { x: number; y: number };
    const size = this.getInputValue('size') as { x: number; y: number };
    const title = this.getInputValue('title') as string;
    const resizable = this.getInputValue('resizable') as boolean;
    const draggable = this.getInputValue('draggable') as boolean;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建面板
      const panelElement = (uiSystem as any).createPanel({
        position,
        size,
        title,
        resizable,
        draggable,
        style,
        onClose: () => {
          this.triggerFlow('onClose');
        },
        onResize: (newSize: { x: number; y: number }) => {
          this.triggerFlow('onResize');
        }
      });

      // 设置输出值
      this.setOutputValue('panelElement', panelElement);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建面板失败:', error);
      return false;
    }
  }
}

/**
 * 创建下拉选择框节点
 */
export class CreateSelectNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('options', 'array', '选项列表', []);
    this.addInputSlot('placeholder', 'string', '占位符', '请选择...');
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('size', 'vector2', '大小', { x: 200, y: 30 });
    this.addInputSlot('multiple', 'boolean', '多选', false);
    this.addInputSlot('style', 'object', '样式', {});

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('onChange', 'flow', '选择改变');
    this.addOutputSlot('selectElement', 'object', '选择框元素');
    this.addOutputSlot('selectedValue', 'any', '选中值');
    this.addOutputSlot('selectedValues', 'array', '选中值列表');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const options = this.getInputValue('options') as any[];
    const placeholder = this.getInputValue('placeholder') as string;
    const position = this.getInputValue('position') as { x: number; y: number };
    const size = this.getInputValue('size') as { x: number; y: number };
    const multiple = this.getInputValue('multiple') as boolean;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建选择框
      const selectElement = (uiSystem as any).createSelect({
        options,
        placeholder,
        position,
        size,
        multiple,
        style,
        onChange: (value: any, values: any[]) => {
          this.setOutputValue('selectedValue', value);
          this.setOutputValue('selectedValues', values);
          this.triggerFlow('onChange');
        }
      });

      // 设置输出值
      this.setOutputValue('selectElement', selectElement);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建选择框失败:', error);
      return false;
    }
  }
}

/**
 * 创建复选框节点
 */
export class CreateCheckboxNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('label', 'string', '标签', '复选框');
    this.addInputSlot('checked', 'boolean', '初始选中', false);
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('disabled', 'boolean', '禁用', false);
    this.addInputSlot('style', 'object', '样式', {});

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('onChange', 'flow', '状态改变');
    this.addOutputSlot('checkboxElement', 'object', '复选框元素');
    this.addOutputSlot('isChecked', 'boolean', '是否选中');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const label = this.getInputValue('label') as string;
    const checked = this.getInputValue('checked') as boolean;
    const position = this.getInputValue('position') as { x: number; y: number };
    const disabled = this.getInputValue('disabled') as boolean;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建复选框
      const checkboxElement = (uiSystem as any).createCheckbox({
        label,
        checked,
        position,
        disabled,
        style,
        onChange: (isChecked: boolean) => {
          this.setOutputValue('isChecked', isChecked);
          this.triggerFlow('onChange');
        }
      });

      // 设置输出值
      this.setOutputValue('checkboxElement', checkboxElement);
      this.setOutputValue('isChecked', checked);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建复选框失败:', error);
      return false;
    }
  }
}

/**
 * 创建单选按钮组节点
 */
export class CreateRadioGroupNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('options', 'array', '选项列表', []);
    this.addInputSlot('name', 'string', '组名', 'radioGroup');
    this.addInputSlot('defaultValue', 'any', '默认值', null);
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('direction', 'string', '排列方向', 'vertical');
    this.addInputSlot('style', 'object', '样式', {});

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('onChange', 'flow', '选择改变');
    this.addOutputSlot('radioGroupElement', 'object', '单选组元素');
    this.addOutputSlot('selectedValue', 'any', '选中值');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const options = this.getInputValue('options') as any[];
    const name = this.getInputValue('name') as string;
    const defaultValue = this.getInputValue('defaultValue') as any;
    const position = this.getInputValue('position') as { x: number; y: number };
    const direction = this.getInputValue('direction') as string;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建单选按钮组
      const radioGroupElement = (uiSystem as any).createRadioGroup({
        options,
        name,
        defaultValue,
        position,
        direction,
        style,
        onChange: (value: any) => {
          this.setOutputValue('selectedValue', value);
          this.triggerFlow('onChange');
        }
      });

      // 设置输出值
      this.setOutputValue('radioGroupElement', radioGroupElement);
      this.setOutputValue('selectedValue', defaultValue);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建单选按钮组失败:', error);
      return false;
    }
  }
}

/**
 * 设置UI元素属性节点
 */
export class SetUIPropertyNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('element', 'object', 'UI元素');
    this.addInputSlot('property', 'string', '属性名', 'visible');
    this.addInputSlot('value', 'any', '属性值', true);

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('fail', 'flow', '失败');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const element = this.getInputValue('element') as any;
    const property = this.getInputValue('property') as string;
    const value = this.getInputValue('value') as any;

    try {
      if (!element) {
        console.error('UI元素为空');
        this.triggerFlow('fail');
        return false;
      }

      // 设置属性
      if (typeof element.setProperty === 'function') {
        element.setProperty(property, value);
      } else {
        element[property] = value;
      }

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('设置UI属性失败:', error);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 创建进度条节点
 */
export class CreateProgressBarNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('value', 'number', '当前值', 0);
    this.addInputSlot('max', 'number', '最大值', 100);
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('size', 'vector2', '大小', { x: 200, y: 20 });
    this.addInputSlot('showText', 'boolean', '显示文本', true);
    this.addInputSlot('style', 'object', '样式', {});

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('progressElement', 'object', '进度条元素');
    this.addOutputSlot('percentage', 'number', '百分比');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const value = this.getInputValue('value') as number;
    const max = this.getInputValue('max') as number;
    const position = this.getInputValue('position') as { x: number; y: number };
    const size = this.getInputValue('size') as { x: number; y: number };
    const showText = this.getInputValue('showText') as boolean;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 计算百分比
      const percentage = Math.min(100, Math.max(0, (value / max) * 100));

      // 创建进度条
      const progressElement = (uiSystem as any).createProgressBar({
        value,
        max,
        position,
        size,
        showText,
        style
      });

      // 设置输出值
      this.setOutputValue('progressElement', progressElement);
      this.setOutputValue('percentage', percentage);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建进度条失败:', error);
      return false;
    }
  }
}

/**
 * 创建模态框节点
 */
export class CreateModalNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('title', 'string', '标题', '模态框');
    this.addInputSlot('content', 'string', '内容', '');
    this.addInputSlot('width', 'number', '宽度', 400);
    this.addInputSlot('height', 'number', '高度', 300);
    this.addInputSlot('closable', 'boolean', '可关闭', true);
    this.addInputSlot('maskClosable', 'boolean', '点击遮罩关闭', true);
    this.addInputSlot('style', 'object', '样式', {});

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('onClose', 'flow', '关闭事件');
    this.addOutputSlot('onOk', 'flow', '确认事件');
    this.addOutputSlot('onCancel', 'flow', '取消事件');
    this.addOutputSlot('modalElement', 'object', '模态框元素');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const title = this.getInputValue('title') as string;
    const content = this.getInputValue('content') as string;
    const width = this.getInputValue('width') as number;
    const height = this.getInputValue('height') as number;
    const closable = this.getInputValue('closable') as boolean;
    const maskClosable = this.getInputValue('maskClosable') as boolean;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建模态框
      const modalElement = (uiSystem as any).createModal({
        title,
        content,
        width,
        height,
        closable,
        maskClosable,
        style,
        onClose: () => {
          this.triggerFlow('onClose');
        },
        onOk: () => {
          this.triggerFlow('onOk');
        },
        onCancel: () => {
          this.triggerFlow('onCancel');
        }
      });

      // 设置输出值
      this.setOutputValue('modalElement', modalElement);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建模态框失败:', error);
      return false;
    }
  }
}

/**
 * 创建标签页节点
 */
export class CreateTabsNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('tabs', 'array', '标签页列表', []);
    this.addInputSlot('activeKey', 'string', '激活标签', '');
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('size', 'vector2', '大小', { x: 400, y: 300 });
    this.addInputSlot('tabPosition', 'string', '标签位置', 'top');
    this.addInputSlot('style', 'object', '样式', {});

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('onChange', 'flow', '标签切换');
    this.addOutputSlot('tabsElement', 'object', '标签页元素');
    this.addOutputSlot('activeTabKey', 'string', '当前激活标签');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const tabs = this.getInputValue('tabs') as any[];
    const activeKey = this.getInputValue('activeKey') as string;
    const position = this.getInputValue('position') as { x: number; y: number };
    const size = this.getInputValue('size') as { x: number; y: number };
    const tabPosition = this.getInputValue('tabPosition') as string;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建标签页
      const tabsElement = (uiSystem as any).createTabs({
        tabs,
        activeKey,
        position,
        size,
        tabPosition,
        style,
        onChange: (key: string) => {
          this.setOutputValue('activeTabKey', key);
          this.triggerFlow('onChange');
        }
      });

      // 设置输出值
      this.setOutputValue('tabsElement', tabsElement);
      this.setOutputValue('activeTabKey', activeKey);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建标签页失败:', error);
      return false;
    }
  }
}

/**
 * 创建颜色选择器节点
 */
export class CreateColorPickerNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('defaultColor', 'string', '默认颜色', '#ffffff');
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('showAlpha', 'boolean', '显示透明度', false);
    this.addInputSlot('showPresets', 'boolean', '显示预设', true);
    this.addInputSlot('presets', 'array', '预设颜色', []);
    this.addInputSlot('style', 'object', '样式', {});

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('onChange', 'flow', '颜色改变');
    this.addOutputSlot('colorPickerElement', 'object', '颜色选择器元素');
    this.addOutputSlot('selectedColor', 'string', '选中颜色');
    this.addOutputSlot('colorRGB', 'object', 'RGB值');
    this.addOutputSlot('colorHSL', 'object', 'HSL值');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const defaultColor = this.getInputValue('defaultColor') as string;
    const position = this.getInputValue('position') as { x: number; y: number };
    const showAlpha = this.getInputValue('showAlpha') as boolean;
    const showPresets = this.getInputValue('showPresets') as boolean;
    const presets = this.getInputValue('presets') as string[];
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建颜色选择器
      const colorPickerElement = (uiSystem as any).createColorPicker({
        defaultColor,
        position,
        showAlpha,
        showPresets,
        presets,
        style,
        onChange: (color: string, rgb: any, hsl: any) => {
          this.setOutputValue('selectedColor', color);
          this.setOutputValue('colorRGB', rgb);
          this.setOutputValue('colorHSL', hsl);
          this.triggerFlow('onChange');
        }
      });

      // 设置输出值
      this.setOutputValue('colorPickerElement', colorPickerElement);
      this.setOutputValue('selectedColor', defaultColor);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建颜色选择器失败:', error);
      return false;
    }
  }
}

/**
 * 注册UI节点
 */
export function registerUINodes(registry: NodeRegistry): void {
  // 注册创建按钮节点
  registry.registerNodeType({
    type: 'ui/button/create',
    category: NodeCategory.UI,
    constructor: CreateButtonNode,
    label: '创建按钮',
    description: '创建一个可点击的按钮',
    icon: 'button',
    color: '#1890ff',
    tags: ['ui', 'button', 'create', 'interactive']
  });

  // 注册创建文本节点
  registry.registerNodeType({
    type: 'ui/text/create',
    category: NodeCategory.UI,
    constructor: CreateTextNode,
    label: '创建文本',
    description: '创建一个文本显示元素',
    icon: 'text',
    color: '#1890ff',
    tags: ['ui', 'text', 'create', 'display']
  });

  // 注册创建输入框节点
  registry.registerNodeType({
    type: 'ui/input/create',
    category: NodeCategory.UI,
    constructor: CreateInputNode,
    label: '创建输入框',
    description: '创建一个文本输入框',
    icon: 'input',
    color: '#1890ff',
    tags: ['ui', 'input', 'create', 'form']
  });

  // 注册创建滑块节点
  registry.registerNodeType({
    type: 'ui/slider/create',
    category: NodeCategory.UI,
    constructor: CreateSliderNode,
    label: '创建滑块',
    description: '创建一个数值滑块',
    icon: 'slider',
    color: '#1890ff',
    tags: ['ui', 'slider', 'create', 'control']
  });

  // 注册创建图像节点
  registry.registerNodeType({
    type: 'ui/image/create',
    category: NodeCategory.UI,
    constructor: CreateImageNode,
    label: '创建图像',
    description: '创建一个图像显示元素',
    icon: 'image',
    color: '#1890ff',
    tags: ['ui', 'image', 'create', 'media']
  });

  // 注册创建面板节点
  registry.registerNodeType({
    type: 'ui/panel/create',
    category: NodeCategory.UI,
    constructor: CreatePanelNode,
    label: '创建面板',
    description: '创建一个可拖拽的面板窗口',
    icon: 'panel',
    color: '#1890ff',
    tags: ['ui', 'panel', 'create', 'window']
  });

  // 注册创建下拉选择框节点
  registry.registerNodeType({
    type: 'ui/select/create',
    category: NodeCategory.UI,
    constructor: CreateSelectNode,
    label: '创建选择框',
    description: '创建一个下拉选择框',
    icon: 'select',
    color: '#1890ff',
    tags: ['ui', 'select', 'create', 'form']
  });

  // 注册创建复选框节点
  registry.registerNodeType({
    type: 'ui/checkbox/create',
    category: NodeCategory.UI,
    constructor: CreateCheckboxNode,
    label: '创建复选框',
    description: '创建一个复选框',
    icon: 'checkbox',
    color: '#1890ff',
    tags: ['ui', 'checkbox', 'create', 'form']
  });

  // 注册创建单选按钮组节点
  registry.registerNodeType({
    type: 'ui/radiogroup/create',
    category: NodeCategory.UI,
    constructor: CreateRadioGroupNode,
    label: '创建单选组',
    description: '创建一个单选按钮组',
    icon: 'radio',
    color: '#1890ff',
    tags: ['ui', 'radio', 'create', 'form']
  });

  // 注册设置UI属性节点
  registry.registerNodeType({
    type: 'ui/property/set',
    category: NodeCategory.UI,
    constructor: SetUIPropertyNode,
    label: '设置UI属性',
    description: '设置UI元素的属性值',
    icon: 'property',
    color: '#1890ff',
    tags: ['ui', 'property', 'set', 'modify']
  });

  // 注册创建进度条节点
  registry.registerNodeType({
    type: 'ui/progressbar/create',
    category: NodeCategory.UI,
    constructor: CreateProgressBarNode,
    label: '创建进度条',
    description: '创建一个进度条',
    icon: 'progress',
    color: '#1890ff',
    tags: ['ui', 'progress', 'create', 'indicator']
  });

  // 注册创建模态框节点
  registry.registerNodeType({
    type: 'ui/modal/create',
    category: NodeCategory.UI,
    constructor: CreateModalNode,
    label: '创建模态框',
    description: '创建一个模态对话框',
    icon: 'modal',
    color: '#1890ff',
    tags: ['ui', 'modal', 'create', 'dialog']
  });

  // 注册创建标签页节点
  registry.registerNodeType({
    type: 'ui/tabs/create',
    category: NodeCategory.UI,
    constructor: CreateTabsNode,
    label: '创建标签页',
    description: '创建一个标签页组件',
    icon: 'tabs',
    color: '#1890ff',
    tags: ['ui', 'tabs', 'create', 'navigation']
  });

  // 注册创建颜色选择器节点
  registry.registerNodeType({
    type: 'ui/colorpicker/create',
    category: NodeCategory.UI,
    constructor: CreateColorPickerNode,
    label: '创建颜色选择器',
    description: '创建一个颜色选择器',
    icon: 'color',
    color: '#1890ff',
    tags: ['ui', 'color', 'create', 'picker']
  });
}
