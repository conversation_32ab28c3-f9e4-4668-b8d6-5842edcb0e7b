/**
 * collaboration.service.ts
 * 
 * 视觉脚本协作服务
 */

import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WebSocketGateway, WebSocketServer, SubscribeMessage, OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { VisualScript } from '../entities/visual-script.entity';
import { ScriptCollaborator, CollaboratorRole, CollaboratorStatus } from '../entities/script-collaborator.entity';
import { InviteCollaboratorDto, UpdateCollaboratorDto } from '../dto/collaboration.dto';

/**
 * 协作操作类型
 */
export enum OperationType {
  NODE_ADD = 'node_add',
  NODE_UPDATE = 'node_update',
  NODE_DELETE = 'node_delete',
  NODE_MOVE = 'node_move',
  CONNECTION_ADD = 'connection_add',
  CONNECTION_DELETE = 'connection_delete',
  PROPERTY_UPDATE = 'property_update',
  CURSOR_MOVE = 'cursor_move',
  SELECTION_CHANGE = 'selection_change'
}

/**
 * 协作操作接口
 */
export interface CollaborationOperation {
  id: string;
  type: OperationType;
  scriptId: string;
  userId: string;
  userName: string;
  timestamp: number;
  data: any;
  version: number;
}

/**
 * 用户光标信息
 */
export interface UserCursor {
  userId: string;
  userName: string;
  position: { x: number; y: number };
  color: string;
  lastUpdate: number;
}

/**
 * 协作会话信息
 */
export interface CollaborationSession {
  scriptId: string;
  users: Map<string, UserCursor>;
  operations: CollaborationOperation[];
  version: number;
  lastActivity: number;
}

@Injectable()
@WebSocketGateway({
  namespace: '/visual-script-collaboration',
  cors: {
    origin: '*',
  },
})
export class CollaborationService implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private sessions: Map<string, CollaborationSession> = new Map();
  private userSockets: Map<string, Socket> = new Map();

  constructor(
    @InjectRepository(VisualScript)
    private readonly scriptRepository: Repository<VisualScript>,
    
    @InjectRepository(ScriptCollaborator)
    private readonly collaboratorRepository: Repository<ScriptCollaborator>
  ) {}

  /**
   * 客户端连接处理
   */
  async handleConnection(client: Socket) {
    console.log(`客户端连接: ${client.id}`);
  }

  /**
   * 客户端断开连接处理
   */
  async handleDisconnect(client: Socket) {
    console.log(`客户端断开连接: ${client.id}`);
    
    // 从所有会话中移除用户
    const userId = client.data.userId;
    if (userId) {
      this.removeUserFromAllSessions(userId);
      this.userSockets.delete(userId);
    }
  }

  /**
   * 加入协作会话
   */
  @SubscribeMessage('join_session')
  async handleJoinSession(
    client: Socket,
    payload: { scriptId: string; userId: string; userName: string }
  ) {
    const { scriptId, userId, userName } = payload;

    try {
      // 验证用户权限
      await this.validateUserAccess(scriptId, userId);

      // 设置客户端数据
      client.data.userId = userId;
      client.data.userName = userName;
      client.data.scriptId = scriptId;

      // 加入房间
      client.join(scriptId);
      this.userSockets.set(userId, client);

      // 获取或创建会话
      let session = this.sessions.get(scriptId);
      if (!session) {
        session = {
          scriptId,
          users: new Map(),
          operations: [],
          version: 0,
          lastActivity: Date.now()
        };
        this.sessions.set(scriptId, session);
      }

      // 添加用户到会话
      const userCursor: UserCursor = {
        userId,
        userName,
        position: { x: 0, y: 0 },
        color: this.generateUserColor(userId),
        lastUpdate: Date.now()
      };
      session.users.set(userId, userCursor);

      // 通知其他用户
      client.to(scriptId).emit('user_joined', {
        userId,
        userName,
        cursor: userCursor
      });

      // 发送当前会话状态给新用户
      client.emit('session_state', {
        users: Array.from(session.users.values()),
        operations: session.operations.slice(-50), // 最近50个操作
        version: session.version
      });

      console.log(`用户 ${userName} 加入脚本 ${scriptId} 的协作会话`);
    } catch (error) {
      client.emit('error', { message: error.message });
    }
  }

  /**
   * 离开协作会话
   */
  @SubscribeMessage('leave_session')
  async handleLeaveSession(client: Socket) {
    const { userId, scriptId } = client.data;

    if (scriptId && userId) {
      const session = this.sessions.get(scriptId);
      if (session) {
        session.users.delete(userId);
        
        // 通知其他用户
        client.to(scriptId).emit('user_left', { userId });

        // 如果会话中没有用户了，清理会话
        if (session.users.size === 0) {
          this.sessions.delete(scriptId);
        }
      }

      client.leave(scriptId);
    }
  }

  /**
   * 处理协作操作
   */
  @SubscribeMessage('operation')
  async handleOperation(
    client: Socket,
    payload: Omit<CollaborationOperation, 'id' | 'timestamp' | 'version'>
  ) {
    const { userId, scriptId } = client.data;
    const session = this.sessions.get(scriptId);

    if (!session) {
      client.emit('error', { message: '会话不存在' });
      return;
    }

    // 创建操作记录
    const operation: CollaborationOperation = {
      id: this.generateOperationId(),
      timestamp: Date.now(),
      version: session.version + 1,
      ...payload
    };

    // 添加到会话操作历史
    session.operations.push(operation);
    session.version = operation.version;
    session.lastActivity = Date.now();

    // 限制操作历史长度
    if (session.operations.length > 1000) {
      session.operations = session.operations.slice(-500);
    }

    // 广播操作给其他用户
    client.to(scriptId).emit('operation', operation);

    // 异步保存到数据库
    this.saveOperationToDatabase(operation).catch(console.error);
  }

  /**
   * 处理光标移动
   */
  @SubscribeMessage('cursor_move')
  async handleCursorMove(
    client: Socket,
    payload: { position: { x: number; y: number } }
  ) {
    const { userId, scriptId } = client.data;
    const session = this.sessions.get(scriptId);

    if (!session) return;

    const userCursor = session.users.get(userId);
    if (userCursor) {
      userCursor.position = payload.position;
      userCursor.lastUpdate = Date.now();

      // 广播光标位置给其他用户
      client.to(scriptId).emit('cursor_update', {
        userId,
        position: payload.position
      });
    }
  }

  /**
   * 邀请协作者
   */
  async inviteCollaborator(
    scriptId: string,
    inviteDto: InviteCollaboratorDto,
    inviterId: string,
    inviterName: string
  ): Promise<ScriptCollaborator> {
    // 验证脚本存在
    const script = await this.scriptRepository.findOne({ where: { id: scriptId } });
    if (!script) {
      throw new NotFoundException('脚本不存在');
    }

    // 验证邀请者权限
    const inviterCollaborator = await this.collaboratorRepository.findOne({
      where: { scriptId, userId: inviterId }
    });

    if (!inviterCollaborator || !inviterCollaborator.canManage) {
      throw new ForbiddenException('没有邀请权限');
    }

    // 检查是否已经是协作者
    const existingCollaborator = await this.collaboratorRepository.findOne({
      where: { scriptId, userId: inviteDto.userId }
    });

    if (existingCollaborator) {
      throw new ForbiddenException('用户已经是协作者');
    }

    // 创建协作者记录
    const collaborator = this.collaboratorRepository.create({
      scriptId,
      userId: inviteDto.userId,
      userName: inviteDto.userName,
      userEmail: inviteDto.userEmail,
      role: inviteDto.role as CollaboratorRole,
      invitedBy: inviterId,
      invitedByName: inviterName,
      invitedAt: new Date(),
      status: CollaboratorStatus.PENDING
    });

    const savedCollaborator = await this.collaboratorRepository.save(collaborator);

    // 发送邀请通知
    this.sendInvitationNotification(savedCollaborator, script);

    return savedCollaborator;
  }

  /**
   * 更新协作者
   */
  async updateCollaborator(
    scriptId: string,
    collaboratorId: string,
    updateDto: UpdateCollaboratorDto,
    updaterId: string
  ): Promise<ScriptCollaborator> {
    const collaborator = await this.collaboratorRepository.findOne({
      where: { id: collaboratorId, scriptId }
    });

    if (!collaborator) {
      throw new NotFoundException('协作者不存在');
    }

    // 验证更新者权限
    const updaterCollaborator = await this.collaboratorRepository.findOne({
      where: { scriptId, userId: updaterId }
    });

    if (!updaterCollaborator || !updaterCollaborator.canManage) {
      throw new ForbiddenException('没有管理权限');
    }

    // 更新协作者信息
    Object.assign(collaborator, updateDto);
    return await this.collaboratorRepository.save(collaborator);
  }

  /**
   * 移除协作者
   */
  async removeCollaborator(
    scriptId: string,
    collaboratorId: string,
    removerId: string
  ): Promise<void> {
    const collaborator = await this.collaboratorRepository.findOne({
      where: { id: collaboratorId, scriptId }
    });

    if (!collaborator) {
      throw new NotFoundException('协作者不存在');
    }

    // 验证移除者权限
    const removerCollaborator = await this.collaboratorRepository.findOne({
      where: { scriptId, userId: removerId }
    });

    if (!removerCollaborator || !removerCollaborator.canManage) {
      throw new ForbiddenException('没有管理权限');
    }

    // 不能移除所有者
    if (collaborator.isOwner) {
      throw new ForbiddenException('不能移除所有者');
    }

    // 标记为已移除
    collaborator.remove();
    await this.collaboratorRepository.save(collaborator);

    // 如果用户在线，通知其被移除
    const userSocket = this.userSockets.get(collaborator.userId);
    if (userSocket) {
      userSocket.emit('removed_from_script', { scriptId });
      userSocket.leave(scriptId);
    }
  }

  /**
   * 验证用户访问权限
   */
  private async validateUserAccess(scriptId: string, userId: string): Promise<void> {
    const script = await this.scriptRepository.findOne({ where: { id: scriptId } });
    if (!script) {
      throw new NotFoundException('脚本不存在');
    }

    // 检查是否是所有者
    if (script.ownerId === userId) {
      return;
    }

    // 检查是否是协作者
    const collaborator = await this.collaboratorRepository.findOne({
      where: { scriptId, userId, status: CollaboratorStatus.ACTIVE }
    });

    if (!collaborator || !collaborator.canView) {
      throw new ForbiddenException('没有访问权限');
    }
  }

  /**
   * 生成用户颜色
   */
  private generateUserColor(userId: string): string {
    const colors = [
      '#1890ff', '#52c41a', '#fa8c16', '#eb2f96',
      '#722ed1', '#13c2c2', '#fadb14', '#f5222d'
    ];
    
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      hash = userId.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  }

  /**
   * 生成操作ID
   */
  private generateOperationId(): string {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 从所有会话中移除用户
   */
  private removeUserFromAllSessions(userId: string): void {
    for (const [scriptId, session] of this.sessions.entries()) {
      if (session.users.has(userId)) {
        session.users.delete(userId);
        
        // 通知其他用户
        this.server.to(scriptId).emit('user_left', { userId });

        // 如果会话中没有用户了，清理会话
        if (session.users.size === 0) {
          this.sessions.delete(scriptId);
        }
      }
    }
  }

  /**
   * 保存操作到数据库
   */
  private async saveOperationToDatabase(operation: CollaborationOperation): Promise<void> {
    // TODO: 实现操作历史的数据库存储
    console.log('保存操作到数据库:', operation);
  }

  /**
   * 发送邀请通知
   */
  private sendInvitationNotification(collaborator: ScriptCollaborator, script: VisualScript): void {
    // TODO: 实现邀请通知发送
    console.log('发送邀请通知:', collaborator, script);
  }
}
