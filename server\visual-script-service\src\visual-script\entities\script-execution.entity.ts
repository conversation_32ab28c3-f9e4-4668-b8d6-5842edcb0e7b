/**
 * script-execution.entity.ts
 * 
 * 脚本执行记录实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index
} from 'typeorm';
import { VisualScript } from './visual-script.entity';

/**
 * 执行状态枚举
 */
export enum ExecutionStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  TIMEOUT = 'timeout'
}

/**
 * 执行环境枚举
 */
export enum ExecutionEnvironment {
  CLIENT = 'client',
  SERVER = 'server',
  EDGE = 'edge',
  CLOUD = 'cloud'
}

/**
 * 脚本执行记录实体
 */
@Entity('script_executions')
@Index(['scriptId', 'status'])
@Index(['executedBy', 'createdAt'])
@Index(['status', 'createdAt'])
@Index(['environment', 'createdAt'])
export class ScriptExecution {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'script_id' })
  @Index()
  scriptId: string;

  @Column({ name: 'script_version', length: 50 })
  scriptVersion: string;

  @Column({
    type: 'enum',
    enum: ExecutionStatus,
    default: ExecutionStatus.PENDING
  })
  status: ExecutionStatus;

  @Column({
    type: 'enum',
    enum: ExecutionEnvironment,
    default: ExecutionEnvironment.CLIENT
  })
  environment: ExecutionEnvironment;

  @Column({ name: 'executed_by' })
  executedBy: string;

  @Column({ name: 'executed_by_name', length: 100 })
  executedByName: string;

  @Column({ name: 'execution_context', type: 'json', nullable: true })
  executionContext: any;

  @Column({ name: 'input_parameters', type: 'json', nullable: true })
  inputParameters: any;

  @Column({ name: 'output_results', type: 'json', nullable: true })
  outputResults: any;

  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string;

  @Column({ name: 'error_stack', type: 'text', nullable: true })
  errorStack: string;

  @Column({ name: 'execution_time_ms', default: 0 })
  executionTimeMs: number;

  @Column({ name: 'memory_usage_mb', type: 'float', default: 0 })
  memoryUsageMb: number;

  @Column({ name: 'cpu_usage_percent', type: 'float', default: 0 })
  cpuUsagePercent: number;

  @Column({ name: 'nodes_executed', default: 0 })
  nodesExecuted: number;

  @Column({ name: 'total_nodes', default: 0 })
  totalNodes: number;

  @Column({ name: 'started_at', nullable: true })
  startedAt: Date;

  @Column({ name: 'completed_at', nullable: true })
  completedAt: Date;

  @Column({ name: 'cancelled_at', nullable: true })
  cancelledAt: Date;

  @Column({ name: 'timeout_at', nullable: true })
  timeoutAt: Date;

  @Column({ name: 'retry_count', default: 0 })
  retryCount: number;

  @Column({ name: 'max_retries', default: 3 })
  maxRetries: number;

  @Column({ name: 'priority', default: 0 })
  priority: number;

  @Column({ type: 'simple-array', nullable: true })
  tags: string[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => VisualScript, script => script.executions, {
    onDelete: 'CASCADE'
  })
  @JoinColumn({ name: 'script_id' })
  script: VisualScript;

  // 计算属性
  get isPending(): boolean {
    return this.status === ExecutionStatus.PENDING;
  }

  get isRunning(): boolean {
    return this.status === ExecutionStatus.RUNNING;
  }

  get isCompleted(): boolean {
    return this.status === ExecutionStatus.COMPLETED;
  }

  get isFailed(): boolean {
    return this.status === ExecutionStatus.FAILED;
  }

  get isCancelled(): boolean {
    return this.status === ExecutionStatus.CANCELLED;
  }

  get isTimeout(): boolean {
    return this.status === ExecutionStatus.TIMEOUT;
  }

  get isFinished(): boolean {
    return this.isCompleted || this.isFailed || this.isCancelled || this.isTimeout;
  }

  get canRetry(): boolean {
    return this.isFailed && this.retryCount < this.maxRetries;
  }

  get progressPercent(): number {
    if (this.totalNodes === 0) return 0;
    return Math.round((this.nodesExecuted / this.totalNodes) * 100);
  }

  get executionTimeSeconds(): number {
    return this.executionTimeMs / 1000;
  }

  // 辅助方法
  start(): void {
    this.status = ExecutionStatus.RUNNING;
    this.startedAt = new Date();
  }

  complete(results?: any): void {
    this.status = ExecutionStatus.COMPLETED;
    this.completedAt = new Date();
    if (results) {
      this.outputResults = results;
    }
    this.calculateExecutionTime();
  }

  fail(error: Error): void {
    this.status = ExecutionStatus.FAILED;
    this.errorMessage = error.message;
    this.errorStack = error.stack;
    this.completedAt = new Date();
    this.calculateExecutionTime();
  }

  cancel(): void {
    this.status = ExecutionStatus.CANCELLED;
    this.cancelledAt = new Date();
    this.calculateExecutionTime();
  }

  timeout(): void {
    this.status = ExecutionStatus.TIMEOUT;
    this.timeoutAt = new Date();
    this.calculateExecutionTime();
  }

  retry(): void {
    if (this.canRetry) {
      this.retryCount++;
      this.status = ExecutionStatus.PENDING;
      this.errorMessage = null;
      this.errorStack = null;
      this.startedAt = null;
      this.completedAt = null;
    }
  }

  updateProgress(nodesExecuted: number, totalNodes: number): void {
    this.nodesExecuted = nodesExecuted;
    this.totalNodes = totalNodes;
  }

  updatePerformanceStats(memoryUsageMb: number, cpuUsagePercent: number): void {
    this.memoryUsageMb = memoryUsageMb;
    this.cpuUsagePercent = cpuUsagePercent;
  }

  private calculateExecutionTime(): void {
    if (this.startedAt) {
      const endTime = this.completedAt || this.cancelledAt || this.timeoutAt || new Date();
      this.executionTimeMs = endTime.getTime() - this.startedAt.getTime();
    }
  }

  addTag(tag: string): void {
    if (!this.tags) {
      this.tags = [];
    }
    if (!this.tags.includes(tag)) {
      this.tags.push(tag);
    }
  }

  removeTag(tag: string): void {
    if (this.tags) {
      this.tags = this.tags.filter(t => t !== tag);
    }
  }

  hasTag(tag: string): boolean {
    return this.tags ? this.tags.includes(tag) : false;
  }

  toJSON(): any {
    return {
      id: this.id,
      scriptId: this.scriptId,
      scriptVersion: this.scriptVersion,
      status: this.status,
      environment: this.environment,
      executedBy: this.executedBy,
      executedByName: this.executedByName,
      executionContext: this.executionContext,
      inputParameters: this.inputParameters,
      outputResults: this.outputResults,
      errorMessage: this.errorMessage,
      executionTimeMs: this.executionTimeMs,
      memoryUsageMb: this.memoryUsageMb,
      cpuUsagePercent: this.cpuUsagePercent,
      nodesExecuted: this.nodesExecuted,
      totalNodes: this.totalNodes,
      progressPercent: this.progressPercent,
      startedAt: this.startedAt,
      completedAt: this.completedAt,
      retryCount: this.retryCount,
      maxRetries: this.maxRetries,
      priority: this.priority,
      tags: this.tags,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}
