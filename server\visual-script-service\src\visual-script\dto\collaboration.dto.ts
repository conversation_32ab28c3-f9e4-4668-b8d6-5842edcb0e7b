/**
 * collaboration.dto.ts
 * 
 * 协作相关的数据传输对象
 */

import { IsString, IsEmail, IsOptional, IsEnum, IsObject, IsArray, IsNumber, IsBoolean } from 'class-validator';
import { CollaboratorRole } from '../entities/script-collaborator.entity';

/**
 * 邀请协作者DTO
 */
export class InviteCollaboratorDto {
  @IsString()
  userId: string;

  @IsString()
  userName: string;

  @IsOptional()
  @IsEmail()
  userEmail?: string;

  @IsEnum(CollaboratorRole)
  role: CollaboratorRole;

  @IsOptional()
  @IsString()
  message?: string;

  @IsOptional()
  @IsBoolean()
  sendNotification?: boolean = true;
}

/**
 * 更新协作者DTO
 */
export class UpdateCollaboratorDto {
  @IsOptional()
  @IsEnum(CollaboratorRole)
  role?: CollaboratorRole;

  @IsOptional()
  @IsObject()
  permissions?: any;

  @IsOptional()
  @IsString()
  notes?: string;
}

/**
 * 协作操作DTO
 */
export class CollaborationOperationDto {
  @IsString()
  type: string;

  @IsString()
  scriptId: string;

  @IsString()
  userId: string;

  @IsString()
  userName: string;

  @IsObject()
  data: any;

  @IsOptional()
  @IsNumber()
  version?: number;
}

/**
 * 光标位置DTO
 */
export class CursorPositionDto {
  @IsNumber()
  x: number;

  @IsNumber()
  y: number;
}

/**
 * 加入会话DTO
 */
export class JoinSessionDto {
  @IsString()
  scriptId: string;

  @IsString()
  userId: string;

  @IsString()
  userName: string;

  @IsOptional()
  @IsString()
  userAvatar?: string;
}

/**
 * 协作会话查询DTO
 */
export class SessionQueryDto {
  @IsOptional()
  @IsString()
  scriptId?: string;

  @IsOptional()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsBoolean()
  includeOperations?: boolean = false;

  @IsOptional()
  @IsNumber()
  operationLimit?: number = 50;
}

/**
 * 协作统计DTO
 */
export class CollaborationStatsDto {
  @IsOptional()
  @IsString()
  scriptId?: string;

  @IsOptional()
  @IsString()
  startDate?: string;

  @IsOptional()
  @IsString()
  endDate?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  metrics?: string[] = ['operations', 'users', 'sessions'];
}

/**
 * 冲突解决DTO
 */
export class ConflictResolutionDto {
  @IsString()
  operationId: string;

  @IsString()
  resolution: 'accept' | 'reject' | 'merge';

  @IsOptional()
  @IsObject()
  mergeData?: any;

  @IsOptional()
  @IsString()
  reason?: string;
}

/**
 * 协作权限DTO
 */
export class CollaborationPermissionDto {
  @IsString()
  scriptId: string;

  @IsString()
  userId: string;

  @IsArray()
  @IsString({ each: true })
  permissions: string[];

  @IsOptional()
  @IsString()
  expiresAt?: string;
}

/**
 * 实时同步DTO
 */
export class RealtimeSyncDto {
  @IsString()
  scriptId: string;

  @IsNumber()
  version: number;

  @IsArray()
  operations: any[];

  @IsOptional()
  @IsObject()
  metadata?: any;
}

/**
 * 协作历史DTO
 */
export class CollaborationHistoryDto {
  @IsString()
  scriptId: string;

  @IsOptional()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsString()
  operationType?: string;

  @IsOptional()
  @IsString()
  startDate?: string;

  @IsOptional()
  @IsString()
  endDate?: string;

  @IsOptional()
  @IsNumber()
  page?: number = 1;

  @IsOptional()
  @IsNumber()
  limit?: number = 50;
}

/**
 * 协作通知DTO
 */
export class CollaborationNotificationDto {
  @IsString()
  type: 'invitation' | 'mention' | 'comment' | 'change';

  @IsString()
  scriptId: string;

  @IsString()
  recipientId: string;

  @IsString()
  senderId: string;

  @IsString()
  senderName: string;

  @IsString()
  message: string;

  @IsOptional()
  @IsObject()
  data?: any;

  @IsOptional()
  @IsBoolean()
  urgent?: boolean = false;
}
