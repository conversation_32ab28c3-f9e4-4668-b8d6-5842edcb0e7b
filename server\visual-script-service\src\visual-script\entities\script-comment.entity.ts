/**
 * script-comment.entity.ts
 * 
 * 脚本评论实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index
} from 'typeorm';
import { VisualScript } from './visual-script.entity';

/**
 * 评论类型枚举
 */
export enum CommentType {
  GENERAL = 'general',
  SUGGESTION = 'suggestion',
  BUG_REPORT = 'bug_report',
  QUESTION = 'question',
  PRAISE = 'praise'
}

/**
 * 评论状态枚举
 */
export enum CommentStatus {
  ACTIVE = 'active',
  HIDDEN = 'hidden',
  DELETED = 'deleted',
  SPAM = 'spam'
}

/**
 * 脚本评论实体
 */
@Entity('script_comments')
@Index(['scriptId', 'status'])
@Index(['authorId', 'createdAt'])
@Index(['parentId'])
@Index(['createdAt'])
export class ScriptComment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'script_id' })
  @Index()
  scriptId: string;

  @Column({ name: 'parent_id', nullable: true })
  @Index()
  parentId: string;

  @Column({ name: 'author_id' })
  authorId: string;

  @Column({ name: 'author_name', length: 100 })
  authorName: string;

  @Column({ name: 'author_avatar', nullable: true })
  authorAvatar: string;

  @Column({
    type: 'enum',
    enum: CommentType,
    default: CommentType.GENERAL
  })
  type: CommentType;

  @Column({
    type: 'enum',
    enum: CommentStatus,
    default: CommentStatus.ACTIVE
  })
  status: CommentStatus;

  @Column({ type: 'text' })
  content: string;

  @Column({ name: 'like_count', default: 0 })
  likeCount: number;

  @Column({ name: 'dislike_count', default: 0 })
  dislikeCount: number;

  @Column({ name: 'reply_count', default: 0 })
  replyCount: number;

  @Column({ name: 'is_pinned', default: false })
  isPinned: boolean;

  @Column({ name: 'is_edited', default: false })
  isEdited: boolean;

  @Column({ name: 'edited_at', nullable: true })
  editedAt: Date;

  @Column({ name: 'mentioned_users', type: 'simple-array', nullable: true })
  mentionedUsers: string[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => VisualScript, script => script.comments, {
    onDelete: 'CASCADE'
  })
  @JoinColumn({ name: 'script_id' })
  script: VisualScript;

  @ManyToOne(() => ScriptComment, comment => comment.replies, {
    onDelete: 'CASCADE'
  })
  @JoinColumn({ name: 'parent_id' })
  parent: ScriptComment;

  @OneToMany(() => ScriptComment, comment => comment.parent)
  replies: ScriptComment[];

  // 计算属性
  get isActive(): boolean {
    return this.status === CommentStatus.ACTIVE;
  }

  get isHidden(): boolean {
    return this.status === CommentStatus.HIDDEN;
  }

  get isDeleted(): boolean {
    return this.status === CommentStatus.DELETED;
  }

  get isSpam(): boolean {
    return this.status === CommentStatus.SPAM;
  }

  get isReply(): boolean {
    return this.parentId !== null;
  }

  get isTopLevel(): boolean {
    return this.parentId === null;
  }

  get netLikes(): number {
    return this.likeCount - this.dislikeCount;
  }

  get hasReplies(): boolean {
    return this.replyCount > 0;
  }

  get typeDisplayName(): string {
    const typeNames = {
      [CommentType.GENERAL]: '一般评论',
      [CommentType.SUGGESTION]: '建议',
      [CommentType.BUG_REPORT]: '错误报告',
      [CommentType.QUESTION]: '问题',
      [CommentType.PRAISE]: '赞扬'
    };
    return typeNames[this.type] || '未知';
  }

  // 辅助方法
  like(): void {
    this.likeCount++;
  }

  unlike(): void {
    if (this.likeCount > 0) {
      this.likeCount--;
    }
  }

  dislike(): void {
    this.dislikeCount++;
  }

  undislike(): void {
    if (this.dislikeCount > 0) {
      this.dislikeCount--;
    }
  }

  pin(): void {
    this.isPinned = true;
    this.updatedAt = new Date();
  }

  unpin(): void {
    this.isPinned = false;
    this.updatedAt = new Date();
  }

  hide(): void {
    this.status = CommentStatus.HIDDEN;
    this.updatedAt = new Date();
  }

  show(): void {
    this.status = CommentStatus.ACTIVE;
    this.updatedAt = new Date();
  }

  markAsSpam(): void {
    this.status = CommentStatus.SPAM;
    this.updatedAt = new Date();
  }

  delete(): void {
    this.status = CommentStatus.DELETED;
    this.updatedAt = new Date();
  }

  edit(newContent: string): void {
    this.content = newContent;
    this.isEdited = true;
    this.editedAt = new Date();
    this.updatedAt = new Date();
  }

  incrementReplyCount(): void {
    this.replyCount++;
  }

  decrementReplyCount(): void {
    if (this.replyCount > 0) {
      this.replyCount--;
    }
  }

  addMentionedUser(userId: string): void {
    if (!this.mentionedUsers) {
      this.mentionedUsers = [];
    }
    if (!this.mentionedUsers.includes(userId)) {
      this.mentionedUsers.push(userId);
    }
  }

  removeMentionedUser(userId: string): void {
    if (this.mentionedUsers) {
      this.mentionedUsers = this.mentionedUsers.filter(id => id !== userId);
    }
  }

  hasMentionedUser(userId: string): boolean {
    return this.mentionedUsers ? this.mentionedUsers.includes(userId) : false;
  }

  updateMetadata(metadata: any): void {
    this.metadata = { ...this.metadata, ...metadata };
    this.updatedAt = new Date();
  }

  /**
   * 检查用户是否可以编辑此评论
   * @param userId 用户ID
   * @returns 是否可以编辑
   */
  canBeEditedBy(userId: string): boolean {
    return this.authorId === userId && this.isActive;
  }

  /**
   * 检查用户是否可以删除此评论
   * @param userId 用户ID
   * @param isAdmin 是否是管理员
   * @returns 是否可以删除
   */
  canBeDeletedBy(userId: string, isAdmin: boolean = false): boolean {
    return this.authorId === userId || isAdmin;
  }

  /**
   * 获取评论的简短预览
   * @param maxLength 最大长度
   * @returns 预览文本
   */
  getPreview(maxLength: number = 100): string {
    if (this.content.length <= maxLength) {
      return this.content;
    }
    return this.content.substring(0, maxLength) + '...';
  }

  toJSON(): any {
    return {
      id: this.id,
      scriptId: this.scriptId,
      parentId: this.parentId,
      authorId: this.authorId,
      authorName: this.authorName,
      authorAvatar: this.authorAvatar,
      type: this.type,
      typeDisplayName: this.typeDisplayName,
      status: this.status,
      content: this.content,
      likeCount: this.likeCount,
      dislikeCount: this.dislikeCount,
      netLikes: this.netLikes,
      replyCount: this.replyCount,
      isPinned: this.isPinned,
      isEdited: this.isEdited,
      editedAt: this.editedAt,
      mentionedUsers: this.mentionedUsers,
      metadata: this.metadata,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}
