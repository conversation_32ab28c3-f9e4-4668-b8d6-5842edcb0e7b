/**
 * script-version.entity.ts
 * 
 * 脚本版本实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index
} from 'typeorm';
import { VisualScript } from './visual-script.entity';

/**
 * 版本类型枚举
 */
export enum VersionType {
  MAJOR = 'major',
  MINOR = 'minor',
  PATCH = 'patch',
  SNAPSHOT = 'snapshot'
}

/**
 * 脚本版本实体
 */
@Entity('script_versions')
@Index(['scriptId', 'version'])
@Index(['scriptId', 'createdAt'])
@Index(['createdAt'])
export class ScriptVersion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'script_id' })
  @Index()
  scriptId: string;

  @Column({ length: 50 })
  version: string;

  @Column({
    type: 'enum',
    enum: VersionType,
    default: VersionType.MINOR
  })
  type: VersionType;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'json' })
  graph: any;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @Column({ type: 'json', nullable: true })
  changes: any;

  @Column({ name: 'created_by' })
  createdBy: string;

  @Column({ name: 'created_by_name', length: 100 })
  createdByName: string;

  @Column({ name: 'file_size', default: 0 })
  fileSize: number;

  @Column({ name: 'node_count', default: 0 })
  nodeCount: number;

  @Column({ name: 'connection_count', default: 0 })
  connectionCount: number;

  @Column({ name: 'is_current', default: false })
  isCurrent: boolean;

  @Column({ name: 'is_stable', default: true })
  isStable: boolean;

  @Column({ type: 'simple-array', nullable: true })
  tags: string[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => VisualScript, script => script.versions, {
    onDelete: 'CASCADE'
  })
  @JoinColumn({ name: 'script_id' })
  script: VisualScript;

  // 计算属性
  get isMajor(): boolean {
    return this.type === VersionType.MAJOR;
  }

  get isMinor(): boolean {
    return this.type === VersionType.MINOR;
  }

  get isPatch(): boolean {
    return this.type === VersionType.PATCH;
  }

  get isSnapshot(): boolean {
    return this.type === VersionType.SNAPSHOT;
  }

  // 辅助方法
  updateStats(nodeCount: number, connectionCount: number, fileSize: number): void {
    this.nodeCount = nodeCount;
    this.connectionCount = connectionCount;
    this.fileSize = fileSize;
  }

  markAsCurrent(): void {
    this.isCurrent = true;
  }

  markAsNotCurrent(): void {
    this.isCurrent = false;
  }

  markAsStable(): void {
    this.isStable = true;
  }

  markAsUnstable(): void {
    this.isStable = false;
  }

  addTag(tag: string): void {
    if (!this.tags) {
      this.tags = [];
    }
    if (!this.tags.includes(tag)) {
      this.tags.push(tag);
    }
  }

  removeTag(tag: string): void {
    if (this.tags) {
      this.tags = this.tags.filter(t => t !== tag);
    }
  }

  hasTag(tag: string): boolean {
    return this.tags ? this.tags.includes(tag) : false;
  }

  /**
   * 比较版本号
   * @param other 另一个版本
   * @returns 比较结果 (-1: 小于, 0: 等于, 1: 大于)
   */
  compareVersion(other: ScriptVersion): number {
    const parseVersion = (version: string): number[] => {
      return version.split('.').map(v => parseInt(v, 10) || 0);
    };

    const thisVersion = parseVersion(this.version);
    const otherVersion = parseVersion(other.version);

    for (let i = 0; i < Math.max(thisVersion.length, otherVersion.length); i++) {
      const thisNum = thisVersion[i] || 0;
      const otherNum = otherVersion[i] || 0;

      if (thisNum < otherNum) return -1;
      if (thisNum > otherNum) return 1;
    }

    return 0;
  }

  /**
   * 检查是否是更新的版本
   * @param other 另一个版本
   * @returns 是否更新
   */
  isNewerThan(other: ScriptVersion): boolean {
    return this.compareVersion(other) > 0;
  }

  /**
   * 检查是否是更旧的版本
   * @param other 另一个版本
   * @returns 是否更旧
   */
  isOlderThan(other: ScriptVersion): boolean {
    return this.compareVersion(other) < 0;
  }

  /**
   * 检查是否是相同版本
   * @param other 另一个版本
   * @returns 是否相同
   */
  isSameVersion(other: ScriptVersion): boolean {
    return this.compareVersion(other) === 0;
  }

  toJSON(): any {
    return {
      id: this.id,
      scriptId: this.scriptId,
      version: this.version,
      type: this.type,
      description: this.description,
      graph: this.graph,
      metadata: this.metadata,
      changes: this.changes,
      createdBy: this.createdBy,
      createdByName: this.createdByName,
      fileSize: this.fileSize,
      nodeCount: this.nodeCount,
      connectionCount: this.connectionCount,
      isCurrent: this.isCurrent,
      isStable: this.isStable,
      tags: this.tags,
      createdAt: this.createdAt
    };
  }
}
