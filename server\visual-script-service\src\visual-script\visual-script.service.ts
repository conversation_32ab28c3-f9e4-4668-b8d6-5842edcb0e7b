/**
 * visual-script.service.ts
 * 
 * 视觉脚本服务
 */

import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, Like, In } from 'typeorm';
import { VisualScript, ScriptStatus, ScriptVisibility } from './entities/visual-script.entity';
import { ScriptVersion } from './entities/script-version.entity';
import { ScriptCollaborator, CollaboratorRole } from './entities/script-collaborator.entity';
import { CreateScriptDto, UpdateScriptDto, ScriptQueryDto } from './dto/script.dto';

@Injectable()
export class VisualScriptService {
  constructor(
    @InjectRepository(VisualScript)
    private readonly scriptRepository: Repository<VisualScript>,
    
    @InjectRepository(ScriptVersion)
    private readonly versionRepository: Repository<ScriptVersion>,
    
    @InjectRepository(ScriptCollaborator)
    private readonly collaboratorRepository: Repository<ScriptCollaborator>
  ) {}

  /**
   * 创建新脚本
   * @param createScriptDto 创建脚本DTO
   * @param userId 用户ID
   * @param userName 用户名
   * @returns 创建的脚本
   */
  async create(createScriptDto: CreateScriptDto, userId: string, userName: string): Promise<VisualScript> {
    const script = this.scriptRepository.create({
      ...createScriptDto,
      ownerId: userId,
      ownerName: userName,
      graph: createScriptDto.graph || { nodes: [], connections: [] },
      metadata: createScriptDto.metadata || {},
      currentVersion: '1.0.0'
    });

    // 计算脚本统计信息
    this.updateScriptStats(script);

    const savedScript = await this.scriptRepository.save(script);

    // 创建初始版本
    await this.createInitialVersion(savedScript, userId, userName);

    // 添加所有者为协作者
    await this.addOwnerAsCollaborator(savedScript.id, userId, userName);

    return savedScript;
  }

  /**
   * 根据ID查找脚本
   * @param id 脚本ID
   * @param userId 当前用户ID
   * @returns 脚本
   */
  async findOne(id: string, userId?: string): Promise<VisualScript> {
    const script = await this.scriptRepository.findOne({
      where: { id },
      relations: ['versions', 'collaborators', 'comments', 'scriptTags']
    });

    if (!script) {
      throw new NotFoundException('脚本不存在');
    }

    // 检查访问权限
    if (userId && !script.canBeViewedBy(userId)) {
      // 检查是否是协作者
      const collaborator = await this.collaboratorRepository.findOne({
        where: { scriptId: id, userId }
      });

      if (!collaborator || !collaborator.canView) {
        throw new ForbiddenException('没有访问权限');
      }
    }

    return script;
  }

  /**
   * 查找多个脚本
   * @param query 查询参数
   * @param userId 当前用户ID
   * @returns 脚本列表
   */
  async findMany(query: ScriptQueryDto, userId?: string): Promise<{ scripts: VisualScript[]; total: number }> {
    const {
      page = 1,
      limit = 20,
      search,
      status,
      visibility,
      ownerId,
      projectId,
      tags,
      sortBy = 'updatedAt',
      sortOrder = 'DESC'
    } = query;

    const queryBuilder = this.scriptRepository.createQueryBuilder('script')
      .leftJoinAndSelect('script.collaborators', 'collaborator')
      .leftJoinAndSelect('script.scriptTags', 'tag');

    // 搜索条件
    if (search) {
      queryBuilder.andWhere(
        '(script.name LIKE :search OR script.description LIKE :search)',
        { search: `%${search}%` }
      );
    }

    // 状态过滤
    if (status) {
      queryBuilder.andWhere('script.status = :status', { status });
    }

    // 可见性过滤
    if (visibility) {
      queryBuilder.andWhere('script.visibility = :visibility', { visibility });
    }

    // 所有者过滤
    if (ownerId) {
      queryBuilder.andWhere('script.ownerId = :ownerId', { ownerId });
    }

    // 项目过滤
    if (projectId) {
      queryBuilder.andWhere('script.projectId = :projectId', { projectId });
    }

    // 标签过滤
    if (tags && tags.length > 0) {
      queryBuilder.andWhere('tag.name IN (:...tags)', { tags });
    }

    // 权限过滤
    if (userId) {
      queryBuilder.andWhere(
        '(script.visibility = :publicVisibility OR script.ownerId = :userId OR collaborator.userId = :userId)',
        { publicVisibility: ScriptVisibility.PUBLIC, userId }
      );
    } else {
      queryBuilder.andWhere('script.visibility = :publicVisibility', {
        publicVisibility: ScriptVisibility.PUBLIC
      });
    }

    // 排序
    queryBuilder.orderBy(`script.${sortBy}`, sortOrder);

    // 分页
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [scripts, total] = await queryBuilder.getManyAndCount();

    return { scripts, total };
  }

  /**
   * 更新脚本
   * @param id 脚本ID
   * @param updateScriptDto 更新脚本DTO
   * @param userId 用户ID
   * @returns 更新后的脚本
   */
  async update(id: string, updateScriptDto: UpdateScriptDto, userId: string): Promise<VisualScript> {
    const script = await this.findOne(id, userId);

    // 检查编辑权限
    if (!script.canBeEditedBy(userId)) {
      const collaborator = await this.collaboratorRepository.findOne({
        where: { scriptId: id, userId }
      });

      if (!collaborator || !collaborator.canEdit) {
        throw new ForbiddenException('没有编辑权限');
      }
    }

    // 更新脚本
    Object.assign(script, updateScriptDto);

    // 如果图形数据有更新，重新计算统计信息
    if (updateScriptDto.graph) {
      this.updateScriptStats(script);
    }

    return await this.scriptRepository.save(script);
  }

  /**
   * 删除脚本
   * @param id 脚本ID
   * @param userId 用户ID
   */
  async remove(id: string, userId: string): Promise<void> {
    const script = await this.findOne(id, userId);

    // 只有所有者可以删除脚本
    if (script.ownerId !== userId) {
      throw new ForbiddenException('只有所有者可以删除脚本');
    }

    // 软删除：标记为已删除状态
    script.status = ScriptStatus.DELETED;
    await this.scriptRepository.save(script);
  }

  /**
   * 发布脚本
   * @param id 脚本ID
   * @param userId 用户ID
   * @returns 发布后的脚本
   */
  async publish(id: string, userId: string): Promise<VisualScript> {
    const script = await this.findOne(id, userId);

    // 检查发布权限
    if (script.ownerId !== userId) {
      throw new ForbiddenException('只有所有者可以发布脚本');
    }

    // 验证脚本是否可以发布
    this.validateScriptForPublish(script);

    script.status = ScriptStatus.PUBLISHED;
    return await this.scriptRepository.save(script);
  }

  /**
   * 归档脚本
   * @param id 脚本ID
   * @param userId 用户ID
   * @returns 归档后的脚本
   */
  async archive(id: string, userId: string): Promise<VisualScript> {
    const script = await this.findOne(id, userId);

    if (script.ownerId !== userId) {
      throw new ForbiddenException('只有所有者可以归档脚本');
    }

    script.status = ScriptStatus.ARCHIVED;
    return await this.scriptRepository.save(script);
  }

  /**
   * 复制脚本
   * @param id 源脚本ID
   * @param userId 用户ID
   * @param userName 用户名
   * @returns 复制的脚本
   */
  async duplicate(id: string, userId: string, userName: string): Promise<VisualScript> {
    const sourceScript = await this.findOne(id, userId);

    const duplicatedScript = this.scriptRepository.create({
      name: `${sourceScript.name} (副本)`,
      description: sourceScript.description,
      projectId: sourceScript.projectId,
      ownerId: userId,
      ownerName: userName,
      graph: JSON.parse(JSON.stringify(sourceScript.graph)),
      metadata: JSON.parse(JSON.stringify(sourceScript.metadata)),
      tags: [...(sourceScript.tags || [])],
      currentVersion: '1.0.0'
    });

    this.updateScriptStats(duplicatedScript);

    const savedScript = await this.scriptRepository.save(duplicatedScript);

    // 创建初始版本
    await this.createInitialVersion(savedScript, userId, userName);

    // 添加所有者为协作者
    await this.addOwnerAsCollaborator(savedScript.id, userId, userName);

    return savedScript;
  }

  /**
   * 更新脚本统计信息
   * @param script 脚本
   */
  private updateScriptStats(script: VisualScript): void {
    const graph = script.graph || { nodes: [], connections: [] };
    script.nodeCount = graph.nodes ? graph.nodes.length : 0;
    script.connectionCount = graph.connections ? graph.connections.length : 0;
    script.fileSize = JSON.stringify(graph).length;
  }

  /**
   * 创建初始版本
   * @param script 脚本
   * @param userId 用户ID
   * @param userName 用户名
   */
  private async createInitialVersion(script: VisualScript, userId: string, userName: string): Promise<void> {
    const version = this.versionRepository.create({
      scriptId: script.id,
      version: '1.0.0',
      description: '初始版本',
      graph: script.graph,
      metadata: script.metadata,
      createdBy: userId,
      createdByName: userName,
      isCurrent: true,
      nodeCount: script.nodeCount,
      connectionCount: script.connectionCount,
      fileSize: script.fileSize
    });

    await this.versionRepository.save(version);
  }

  /**
   * 添加所有者为协作者
   * @param scriptId 脚本ID
   * @param userId 用户ID
   * @param userName 用户名
   */
  private async addOwnerAsCollaborator(scriptId: string, userId: string, userName: string): Promise<void> {
    const collaborator = this.collaboratorRepository.create({
      scriptId,
      userId,
      userName,
      role: CollaboratorRole.OWNER,
      invitedBy: userId,
      invitedByName: userName,
      invitedAt: new Date(),
      acceptedAt: new Date()
    });

    collaborator.accept();
    await this.collaboratorRepository.save(collaborator);
  }

  /**
   * 验证脚本是否可以发布
   * @param script 脚本
   */
  private validateScriptForPublish(script: VisualScript): void {
    if (!script.name || script.name.trim().length === 0) {
      throw new BadRequestException('脚本名称不能为空');
    }

    if (!script.graph || !script.graph.nodes || script.graph.nodes.length === 0) {
      throw new BadRequestException('脚本不能为空');
    }

    // 可以添加更多验证规则
  }
}
