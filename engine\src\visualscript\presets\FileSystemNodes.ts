/**
 * FileSystemNodes.ts
 * 
 * 文件系统相关的视觉脚本节点
 */

import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

/**
 * 读取文本文件节点
 */
export class ReadTextFileNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('filePath', 'string', '文件路径', '');
    this.addInputSlot('encoding', 'string', '编码', 'utf-8');
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('fail', 'flow', '失败');
    this.addOutputSlot('content', 'string', '文件内容');
    this.addOutputSlot('error', 'string', '错误信息');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const filePath = this.getInputValue('filePath') as string;
    const encoding = this.getInputValue('encoding') as string;

    try {
      // 获取文件系统服务
      const fileSystem = context.world?.getService('FileSystem');
      if (!fileSystem) {
        const error = '文件系统服务未找到';
        this.setOutputValue('error', error);
        this.triggerFlow('fail');
        return false;
      }

      // 读取文件
      const content = await (fileSystem as any).readTextFile(filePath, encoding);
      
      // 设置输出值
      this.setOutputValue('content', content);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('读取文件失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 写入文本文件节点
 */
export class WriteTextFileNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('filePath', 'string', '文件路径', '');
    this.addInputSlot('content', 'string', '文件内容', '');
    this.addInputSlot('encoding', 'string', '编码', 'utf-8');
    this.addInputSlot('append', 'boolean', '追加模式', false);
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('fail', 'flow', '失败');
    this.addOutputSlot('error', 'string', '错误信息');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const filePath = this.getInputValue('filePath') as string;
    const content = this.getInputValue('content') as string;
    const encoding = this.getInputValue('encoding') as string;
    const append = this.getInputValue('append') as boolean;

    try {
      // 获取文件系统服务
      const fileSystem = context.world?.getService('FileSystem');
      if (!fileSystem) {
        const error = '文件系统服务未找到';
        this.setOutputValue('error', error);
        this.triggerFlow('fail');
        return false;
      }

      // 写入文件
      await (fileSystem as any).writeTextFile(filePath, content, { encoding, append });

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('写入文件失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 读取JSON文件节点
 */
export class ReadJSONFileNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('filePath', 'string', '文件路径', '');
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('fail', 'flow', '失败');
    this.addOutputSlot('data', 'object', 'JSON数据');
    this.addOutputSlot('error', 'string', '错误信息');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const filePath = this.getInputValue('filePath') as string;

    try {
      // 获取文件系统服务
      const fileSystem = context.world?.getService('FileSystem');
      if (!fileSystem) {
        const error = '文件系统服务未找到';
        this.setOutputValue('error', error);
        this.triggerFlow('fail');
        return false;
      }

      // 读取JSON文件
      const data = await (fileSystem as any).readJSONFile(filePath);
      
      // 设置输出值
      this.setOutputValue('data', data);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('读取JSON文件失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 写入JSON文件节点
 */
export class WriteJSONFileNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('filePath', 'string', '文件路径', '');
    this.addInputSlot('data', 'object', 'JSON数据', {});
    this.addInputSlot('indent', 'number', '缩进空格', 2);
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('fail', 'flow', '失败');
    this.addOutputSlot('error', 'string', '错误信息');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const filePath = this.getInputValue('filePath') as string;
    const data = this.getInputValue('data') as any;
    const indent = this.getInputValue('indent') as number;

    try {
      // 获取文件系统服务
      const fileSystem = context.world?.getService('FileSystem');
      if (!fileSystem) {
        const error = '文件系统服务未找到';
        this.setOutputValue('error', error);
        this.triggerFlow('fail');
        return false;
      }

      // 写入JSON文件
      await (fileSystem as any).writeJSONFile(filePath, data, { indent });

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('写入JSON文件失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 检查文件是否存在节点
 */
export class FileExistsNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('filePath', 'string', '文件路径', '');
    
    // 输出插槽
    this.addOutputSlot('exists', 'flow', '文件存在');
    this.addOutputSlot('notExists', 'flow', '文件不存在');
    this.addOutputSlot('result', 'boolean', '检查结果');
    this.addOutputSlot('error', 'string', '错误信息');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const filePath = this.getInputValue('filePath') as string;

    try {
      // 获取文件系统服务
      const fileSystem = context.world?.getService('FileSystem');
      if (!fileSystem) {
        const error = '文件系统服务未找到';
        this.setOutputValue('error', error);
        return false;
      }

      // 检查文件是否存在
      const exists = await (fileSystem as any).exists(filePath);
      
      // 设置输出值
      this.setOutputValue('result', exists);

      // 触发相应流程
      if (exists) {
        this.triggerFlow('exists');
      } else {
        this.triggerFlow('notExists');
      }
      
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('检查文件存在失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      return false;
    }
  }
}

/**
 * 列出目录内容节点
 */
export class ListDirectoryNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('directoryPath', 'string', '目录路径', '');
    this.addInputSlot('recursive', 'boolean', '递归列出', false);
    this.addInputSlot('filter', 'string', '文件过滤器', '*');
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('fail', 'flow', '失败');
    this.addOutputSlot('files', 'array', '文件列表');
    this.addOutputSlot('directories', 'array', '目录列表');
    this.addOutputSlot('error', 'string', '错误信息');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const directoryPath = this.getInputValue('directoryPath') as string;
    const recursive = this.getInputValue('recursive') as boolean;
    const filter = this.getInputValue('filter') as string;

    try {
      // 获取文件系统服务
      const fileSystem = context.world?.getService('FileSystem');
      if (!fileSystem) {
        const error = '文件系统服务未找到';
        this.setOutputValue('error', error);
        this.triggerFlow('fail');
        return false;
      }

      // 列出目录内容
      const result = await (fileSystem as any).listDirectory(directoryPath, { recursive, filter });
      
      // 设置输出值
      this.setOutputValue('files', result.files || []);
      this.setOutputValue('directories', result.directories || []);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('列出目录内容失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 创建目录节点
 */
export class CreateDirectoryNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('directoryPath', 'string', '目录路径', '');
    this.addInputSlot('recursive', 'boolean', '递归创建', true);

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('fail', 'flow', '失败');
    this.addOutputSlot('error', 'string', '错误信息');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const directoryPath = this.getInputValue('directoryPath') as string;
    const recursive = this.getInputValue('recursive') as boolean;

    try {
      // 获取文件系统服务
      const fileSystem = context.world?.getService('FileSystem');
      if (!fileSystem) {
        const error = '文件系统服务未找到';
        this.setOutputValue('error', error);
        this.triggerFlow('fail');
        return false;
      }

      // 创建目录
      await (fileSystem as any).createDirectory(directoryPath, { recursive });

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('创建目录失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 删除文件节点
 */
export class DeleteFileNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('filePath', 'string', '文件路径', '');
    this.addInputSlot('force', 'boolean', '强制删除', false);

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('fail', 'flow', '失败');
    this.addOutputSlot('error', 'string', '错误信息');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const filePath = this.getInputValue('filePath') as string;
    const force = this.getInputValue('force') as boolean;

    try {
      // 获取文件系统服务
      const fileSystem = context.world?.getService('FileSystem');
      if (!fileSystem) {
        const error = '文件系统服务未找到';
        this.setOutputValue('error', error);
        this.triggerFlow('fail');
        return false;
      }

      // 删除文件
      await (fileSystem as any).deleteFile(filePath, { force });

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('删除文件失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 复制文件节点
 */
export class CopyFileNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('sourcePath', 'string', '源文件路径', '');
    this.addInputSlot('targetPath', 'string', '目标文件路径', '');
    this.addInputSlot('overwrite', 'boolean', '覆盖已存在', false);

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('fail', 'flow', '失败');
    this.addOutputSlot('error', 'string', '错误信息');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const sourcePath = this.getInputValue('sourcePath') as string;
    const targetPath = this.getInputValue('targetPath') as string;
    const overwrite = this.getInputValue('overwrite') as boolean;

    try {
      // 获取文件系统服务
      const fileSystem = context.world?.getService('FileSystem');
      if (!fileSystem) {
        const error = '文件系统服务未找到';
        this.setOutputValue('error', error);
        this.triggerFlow('fail');
        return false;
      }

      // 复制文件
      await (fileSystem as any).copyFile(sourcePath, targetPath, { overwrite });

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('复制文件失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 移动文件节点
 */
export class MoveFileNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('sourcePath', 'string', '源文件路径', '');
    this.addInputSlot('targetPath', 'string', '目标文件路径', '');
    this.addInputSlot('overwrite', 'boolean', '覆盖已存在', false);

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('fail', 'flow', '失败');
    this.addOutputSlot('error', 'string', '错误信息');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const sourcePath = this.getInputValue('sourcePath') as string;
    const targetPath = this.getInputValue('targetPath') as string;
    const overwrite = this.getInputValue('overwrite') as boolean;

    try {
      // 获取文件系统服务
      const fileSystem = context.world?.getService('FileSystem');
      if (!fileSystem) {
        const error = '文件系统服务未找到';
        this.setOutputValue('error', error);
        this.triggerFlow('fail');
        return false;
      }

      // 移动文件
      await (fileSystem as any).moveFile(sourcePath, targetPath, { overwrite });

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('移动文件失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 注册文件系统节点
 */
export function registerFileSystemNodes(registry: NodeRegistry): void {
  // 注册读取文本文件节点
  registry.registerNodeType({
    type: 'file/read/text',
    category: NodeCategory.FILE,
    constructor: ReadTextFileNode,
    label: '读取文本文件',
    description: '读取文本文件内容',
    icon: 'file-text',
    color: '#52c41a',
    tags: ['file', 'read', 'text', 'io']
  });

  // 注册写入文本文件节点
  registry.registerNodeType({
    type: 'file/write/text',
    category: NodeCategory.FILE,
    constructor: WriteTextFileNode,
    label: '写入文本文件',
    description: '写入文本内容到文件',
    icon: 'file-text',
    color: '#52c41a',
    tags: ['file', 'write', 'text', 'io']
  });

  // 注册读取JSON文件节点
  registry.registerNodeType({
    type: 'file/read/json',
    category: NodeCategory.FILE,
    constructor: ReadJSONFileNode,
    label: '读取JSON文件',
    description: '读取JSON文件并解析为对象',
    icon: 'file-json',
    color: '#52c41a',
    tags: ['file', 'read', 'json', 'data']
  });

  // 注册写入JSON文件节点
  registry.registerNodeType({
    type: 'file/write/json',
    category: NodeCategory.FILE,
    constructor: WriteJSONFileNode,
    label: '写入JSON文件',
    description: '将对象序列化为JSON并写入文件',
    icon: 'file-json',
    color: '#52c41a',
    tags: ['file', 'write', 'json', 'data']
  });

  // 注册检查文件存在节点
  registry.registerNodeType({
    type: 'file/exists',
    category: NodeCategory.FILE,
    constructor: FileExistsNode,
    label: '检查文件存在',
    description: '检查文件或目录是否存在',
    icon: 'file-search',
    color: '#52c41a',
    tags: ['file', 'exists', 'check', 'validation']
  });

  // 注册列出目录内容节点
  registry.registerNodeType({
    type: 'file/directory/list',
    category: NodeCategory.FILE,
    constructor: ListDirectoryNode,
    label: '列出目录内容',
    description: '列出目录中的文件和子目录',
    icon: 'folder-open',
    color: '#52c41a',
    tags: ['file', 'directory', 'list', 'browse']
  });

  // 注册创建目录节点
  registry.registerNodeType({
    type: 'file/directory/create',
    category: NodeCategory.FILE,
    constructor: CreateDirectoryNode,
    label: '创建目录',
    description: '创建新目录',
    icon: 'folder-add',
    color: '#52c41a',
    tags: ['file', 'directory', 'create', 'mkdir']
  });

  // 注册删除文件节点
  registry.registerNodeType({
    type: 'file/delete',
    category: NodeCategory.FILE,
    constructor: DeleteFileNode,
    label: '删除文件',
    description: '删除指定文件',
    icon: 'delete',
    color: '#ff4d4f',
    tags: ['file', 'delete', 'remove', 'unlink']
  });

  // 注册复制文件节点
  registry.registerNodeType({
    type: 'file/copy',
    category: NodeCategory.FILE,
    constructor: CopyFileNode,
    label: '复制文件',
    description: '复制文件到指定位置',
    icon: 'copy',
    color: '#52c41a',
    tags: ['file', 'copy', 'duplicate', 'backup']
  });

  // 注册移动文件节点
  registry.registerNodeType({
    type: 'file/move',
    category: NodeCategory.FILE,
    constructor: MoveFileNode,
    label: '移动文件',
    description: '移动文件到指定位置',
    icon: 'drag',
    color: '#52c41a',
    tags: ['file', 'move', 'rename', 'relocate']
  });
}
