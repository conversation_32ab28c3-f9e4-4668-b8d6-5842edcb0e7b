/**
 * CollaborationPanel.tsx
 * 
 * 协作面板组件
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  List,
  Avatar,
  Button,
  Input,
  Select,
  Modal,
  Badge,
  Tooltip,
  Space,
  Typography,
  Divider,
  Tag,
  Popover,
  message
} from 'antd';
import {
  UserAddOutlined,
  UserOutlined,
  CrownOutlined,
  EditOutlined,
  EyeOutlined,
  MessageOutlined,
  MoreOutlined,
  OnlineOutlined,
  OfflineOutlined,
  LockOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { io, Socket } from 'socket.io-client';

const { Title, Text } = Typography;
const { Option } = Select;

/**
 * 用户状态接口
 */
interface UserStatus {
  userId: string;
  userName: string;
  userAvatar?: string;
  role: 'owner' | 'editor' | 'viewer' | 'commenter';
  status: 'online' | 'offline' | 'away';
  cursor?: { x: number; y: number };
  color: string;
  lastActive: Date;
}

/**
 * 协作操作接口
 */
interface CollaborationOperation {
  id: string;
  type: string;
  userId: string;
  userName: string;
  timestamp: number;
  description: string;
}

/**
 * 协作面板属性
 */
interface CollaborationPanelProps {
  scriptId: string;
  currentUserId: string;
  currentUserName: string;
  onInviteUser?: (userInfo: any) => void;
  onUserClick?: (userId: string) => void;
}

/**
 * 协作面板组件
 */
const CollaborationPanel: React.FC<CollaborationPanelProps> = ({
  scriptId,
  currentUserId,
  currentUserName,
  onInviteUser,
  onUserClick
}) => {
  const { t } = useTranslation();
  const [socket, setSocket] = useState<Socket | null>(null);
  const [activeUsers, setActiveUsers] = useState<UserStatus[]>([]);
  const [recentOperations, setRecentOperations] = useState<CollaborationOperation[]>([]);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<string>('viewer');
  const [isConnected, setIsConnected] = useState(false);

  // 初始化WebSocket连接
  useEffect(() => {
    const newSocket = io('/visual-script-collaboration', {
      auth: {
        userId: currentUserId,
        userName: currentUserName
      }
    });

    newSocket.on('connect', () => {
      setIsConnected(true);
      console.log('协作连接已建立');
      
      // 加入脚本协作会话
      newSocket.emit('join_session', {
        scriptId,
        userId: currentUserId,
        userName: currentUserName
      });
    });

    newSocket.on('disconnect', () => {
      setIsConnected(false);
      console.log('协作连接已断开');
    });

    newSocket.on('session_state', (data: any) => {
      setActiveUsers(data.users || []);
      setRecentOperations(data.operations || []);
    });

    newSocket.on('user_joined', (data: any) => {
      setActiveUsers(prev => {
        const existing = prev.find(user => user.userId === data.userId);
        if (existing) {
          return prev.map(user => 
            user.userId === data.userId 
              ? { ...user, status: 'online' as const }
              : user
          );
        }
        return [...prev, {
          userId: data.userId,
          userName: data.userName,
          userAvatar: data.userAvatar,
          role: data.role || 'viewer',
          status: 'online' as const,
          color: data.cursor?.color || '#1890ff',
          lastActive: new Date()
        }];
      });
      
      message.info(`${data.userName} ${t('协作.加入了协作')}`);
    });

    newSocket.on('user_left', (data: any) => {
      setActiveUsers(prev => 
        prev.map(user => 
          user.userId === data.userId 
            ? { ...user, status: 'offline' as const }
            : user
        )
      );
    });

    newSocket.on('operation', (operation: CollaborationOperation) => {
      setRecentOperations(prev => [operation, ...prev.slice(0, 49)]);
    });

    newSocket.on('cursor_update', (data: any) => {
      setActiveUsers(prev => 
        prev.map(user => 
          user.userId === data.userId 
            ? { ...user, cursor: data.position }
            : user
        )
      );
    });

    newSocket.on('error', (error: any) => {
      message.error(error.message || t('协作.连接错误'));
    });

    setSocket(newSocket);

    return () => {
      newSocket.disconnect();
    };
  }, [scriptId, currentUserId, currentUserName, t]);

  // 邀请用户
  const handleInviteUser = useCallback(async () => {
    if (!inviteEmail.trim()) {
      message.error(t('协作.请输入邮箱地址'));
      return;
    }

    try {
      // TODO: 调用API邀请用户
      console.log('邀请用户:', { email: inviteEmail, role: inviteRole });
      
      if (onInviteUser) {
        onInviteUser({ email: inviteEmail, role: inviteRole });
      }

      message.success(t('协作.邀请已发送'));
      setShowInviteModal(false);
      setInviteEmail('');
      setInviteRole('viewer');
    } catch (error) {
      message.error(t('协作.邀请失败'));
    }
  }, [inviteEmail, inviteRole, onInviteUser, t]);

  // 获取角色图标
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <CrownOutlined style={{ color: '#faad14' }} />;
      case 'editor':
        return <EditOutlined style={{ color: '#52c41a' }} />;
      case 'commenter':
        return <MessageOutlined style={{ color: '#1890ff' }} />;
      case 'viewer':
      default:
        return <EyeOutlined style={{ color: '#8c8c8c' }} />;
    }
  };

  // 获取角色标签
  const getRoleTag = (role: string) => {
    const roleConfig = {
      owner: { color: 'gold', text: t('协作.所有者') },
      editor: { color: 'green', text: t('协作.编辑者') },
      commenter: { color: 'blue', text: t('协作.评论者') },
      viewer: { color: 'default', text: t('协作.查看者') }
    };

    const config = roleConfig[role as keyof typeof roleConfig] || roleConfig.viewer;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 渲染用户操作菜单
  const renderUserActions = (user: UserStatus) => {
    if (user.userId === currentUserId) return null;

    return (
      <Popover
        content={
          <Space direction="vertical">
            <Button size="small" onClick={() => onUserClick?.(user.userId)}>
              {t('协作.查看用户')}
            </Button>
            <Button size="small">
              {t('协作.发送消息')}
            </Button>
            {user.role !== 'owner' && (
              <Button size="small" danger>
                {t('协作.移除用户')}
              </Button>
            )}
          </Space>
        }
        trigger="click"
      >
        <Button type="text" size="small" icon={<MoreOutlined />} />
      </Popover>
    );
  };

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Space>
            <span>{t('协作.协作面板')}</span>
            <Badge 
              status={isConnected ? 'success' : 'error'} 
              text={isConnected ? t('协作.已连接') : t('协作.未连接')}
            />
          </Space>
          <Button
            type="primary"
            size="small"
            icon={<UserAddOutlined />}
            onClick={() => setShowInviteModal(true)}
          >
            {t('协作.邀请')}
          </Button>
        </div>
      }
      style={{ height: '100%' }}
      bodyStyle={{ padding: '16px', height: 'calc(100% - 60px)', overflow: 'auto' }}
    >
      {/* 在线用户列表 */}
      <div style={{ marginBottom: '16px' }}>
        <Title level={5}>{t('协作.在线用户')} ({activeUsers.filter(u => u.status === 'online').length})</Title>
        <List
          size="small"
          dataSource={activeUsers}
          renderItem={(user) => (
            <List.Item
              actions={[renderUserActions(user)]}
              style={{ 
                opacity: user.status === 'offline' ? 0.6 : 1,
                borderLeft: `3px solid ${user.color}`,
                paddingLeft: '8px'
              }}
            >
              <List.Item.Meta
                avatar={
                  <Badge 
                    dot 
                    status={user.status === 'online' ? 'success' : 'default'}
                    offset={[-2, 2]}
                  >
                    <Avatar 
                      src={user.userAvatar} 
                      icon={<UserOutlined />}
                      style={{ backgroundColor: user.color }}
                    >
                      {user.userName.charAt(0).toUpperCase()}
                    </Avatar>
                  </Badge>
                }
                title={
                  <Space>
                    <span>{user.userName}</span>
                    {getRoleIcon(user.role)}
                  </Space>
                }
                description={
                  <Space>
                    {getRoleTag(user.role)}
                    {user.status === 'online' && (
                      <Tag color="green" icon={<OnlineOutlined />}>
                        {t('协作.在线')}
                      </Tag>
                    )}
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      </div>

      <Divider />

      {/* 最近操作 */}
      <div>
        <Title level={5}>{t('协作.最近操作')}</Title>
        <List
          size="small"
          dataSource={recentOperations.slice(0, 10)}
          renderItem={(operation) => (
            <List.Item>
              <List.Item.Meta
                avatar={
                  <Avatar size="small" style={{ backgroundColor: '#1890ff' }}>
                    {operation.userName.charAt(0).toUpperCase()}
                  </Avatar>
                }
                title={
                  <Text style={{ fontSize: '12px' }}>
                    <strong>{operation.userName}</strong> {operation.description}
                  </Text>
                }
                description={
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    {new Date(operation.timestamp).toLocaleTimeString()}
                  </Text>
                }
              />
            </List.Item>
          )}
        />
      </div>

      {/* 邀请用户模态框 */}
      <Modal
        title={t('协作.邀请用户')}
        open={showInviteModal}
        onOk={handleInviteUser}
        onCancel={() => setShowInviteModal(false)}
        okText={t('协作.发送邀请')}
        cancelText={t('协作.取消')}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>{t('协作.邮箱地址')}</Text>
            <Input
              value={inviteEmail}
              onChange={(e) => setInviteEmail(e.target.value)}
              placeholder={t('协作.请输入邮箱地址')}
              style={{ marginTop: '4px' }}
            />
          </div>
          
          <div>
            <Text strong>{t('协作.角色权限')}</Text>
            <Select
              value={inviteRole}
              onChange={setInviteRole}
              style={{ width: '100%', marginTop: '4px' }}
            >
              <Option value="viewer">{t('协作.查看者')} - {t('协作.只能查看')}</Option>
              <Option value="commenter">{t('协作.评论者')} - {t('协作.可以评论')}</Option>
              <Option value="editor">{t('协作.编辑者')} - {t('协作.可以编辑')}</Option>
            </Select>
          </div>
        </Space>
      </Modal>
    </Card>
  );
};

export default CollaborationPanel;
