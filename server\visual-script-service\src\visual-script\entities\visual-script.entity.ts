/**
 * visual-script.entity.ts
 * 
 * 视觉脚本实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index
} from 'typeorm';
import { ScriptVersion } from './script-version.entity';
import { ScriptExecution } from './script-execution.entity';
import { ScriptCollaborator } from './script-collaborator.entity';
import { ScriptComment } from './script-comment.entity';
import { ScriptTag } from './script-tag.entity';

/**
 * 脚本状态枚举
 */
export enum ScriptStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  DELETED = 'deleted'
}

/**
 * 脚本可见性枚举
 */
export enum ScriptVisibility {
  PRIVATE = 'private',
  PUBLIC = 'public',
  SHARED = 'shared'
}

/**
 * 视觉脚本实体
 */
@Entity('visual_scripts')
@Index(['ownerId', 'status'])
@Index(['projectId', 'status'])
@Index(['createdAt'])
@Index(['updatedAt'])
export class VisualScript {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  @Index()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'project_id', nullable: true })
  @Index()
  projectId: string;

  @Column({ name: 'owner_id' })
  @Index()
  ownerId: string;

  @Column({ name: 'owner_name', length: 100 })
  ownerName: string;

  @Column({
    type: 'enum',
    enum: ScriptStatus,
    default: ScriptStatus.DRAFT
  })
  status: ScriptStatus;

  @Column({
    type: 'enum',
    enum: ScriptVisibility,
    default: ScriptVisibility.PRIVATE
  })
  visibility: ScriptVisibility;

  @Column({ type: 'json' })
  graph: any;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @Column({ name: 'current_version', length: 50, default: '1.0.0' })
  currentVersion: string;

  @Column({ name: 'execution_count', default: 0 })
  executionCount: number;

  @Column({ name: 'last_executed_at', nullable: true })
  lastExecutedAt: Date;

  @Column({ name: 'is_template', default: false })
  isTemplate: boolean;

  @Column({ name: 'template_category', nullable: true })
  templateCategory: string;

  @Column({ type: 'simple-array', nullable: true })
  tags: string[];

  @Column({ name: 'file_size', default: 0 })
  fileSize: number;

  @Column({ name: 'node_count', default: 0 })
  nodeCount: number;

  @Column({ name: 'connection_count', default: 0 })
  connectionCount: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => ScriptVersion, version => version.script)
  versions: ScriptVersion[];

  @OneToMany(() => ScriptExecution, execution => execution.script)
  executions: ScriptExecution[];

  @OneToMany(() => ScriptCollaborator, collaborator => collaborator.script)
  collaborators: ScriptCollaborator[];

  @OneToMany(() => ScriptComment, comment => comment.script)
  comments: ScriptComment[];

  @OneToMany(() => ScriptTag, tag => tag.script)
  scriptTags: ScriptTag[];

  // 计算属性
  get isPublic(): boolean {
    return this.visibility === ScriptVisibility.PUBLIC;
  }

  get isPrivate(): boolean {
    return this.visibility === ScriptVisibility.PRIVATE;
  }

  get isShared(): boolean {
    return this.visibility === ScriptVisibility.SHARED;
  }

  get isDraft(): boolean {
    return this.status === ScriptStatus.DRAFT;
  }

  get isPublished(): boolean {
    return this.status === ScriptStatus.PUBLISHED;
  }

  get isArchived(): boolean {
    return this.status === ScriptStatus.ARCHIVED;
  }

  get isDeleted(): boolean {
    return this.status === ScriptStatus.DELETED;
  }

  // 辅助方法
  updateStats(nodeCount: number, connectionCount: number, fileSize: number): void {
    this.nodeCount = nodeCount;
    this.connectionCount = connectionCount;
    this.fileSize = fileSize;
    this.updatedAt = new Date();
  }

  incrementExecutionCount(): void {
    this.executionCount++;
    this.lastExecutedAt = new Date();
  }

  addTag(tag: string): void {
    if (!this.tags) {
      this.tags = [];
    }
    if (!this.tags.includes(tag)) {
      this.tags.push(tag);
    }
  }

  removeTag(tag: string): void {
    if (this.tags) {
      this.tags = this.tags.filter(t => t !== tag);
    }
  }

  hasTag(tag: string): boolean {
    return this.tags ? this.tags.includes(tag) : false;
  }

  canBeEditedBy(userId: string): boolean {
    return this.ownerId === userId || this.isShared;
  }

  canBeViewedBy(userId: string): boolean {
    return this.ownerId === userId || this.isPublic || this.isShared;
  }

  toJSON(): any {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      projectId: this.projectId,
      ownerId: this.ownerId,
      ownerName: this.ownerName,
      status: this.status,
      visibility: this.visibility,
      graph: this.graph,
      metadata: this.metadata,
      currentVersion: this.currentVersion,
      executionCount: this.executionCount,
      lastExecutedAt: this.lastExecutedAt,
      isTemplate: this.isTemplate,
      templateCategory: this.templateCategory,
      tags: this.tags,
      fileSize: this.fileSize,
      nodeCount: this.nodeCount,
      connectionCount: this.connectionCount,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}
