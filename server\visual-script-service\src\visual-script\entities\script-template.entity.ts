/**
 * script-template.entity.ts
 * 
 * 脚本模板实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index
} from 'typeorm';

/**
 * 模板类型枚举
 */
export enum TemplateType {
  BASIC = 'basic',
  ADVANCED = 'advanced',
  GAME = 'game',
  EDUCATION = 'education',
  BUSINESS = 'business',
  UTILITY = 'utility',
  DEMO = 'demo'
}

/**
 * 模板状态枚举
 */
export enum TemplateStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  FEATURED = 'featured',
  DEPRECATED = 'deprecated',
  ARCHIVED = 'archived'
}

/**
 * 脚本模板实体
 */
@Entity('script_templates')
@Index(['category', 'status'])
@Index(['type', 'status'])
@Index(['featured', 'createdAt'])
@Index(['downloadCount'])
export class ScriptTemplate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  @Index()
  name: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ length: 100 })
  @Index()
  category: string;

  @Column({
    type: 'enum',
    enum: TemplateType,
    default: TemplateType.BASIC
  })
  type: TemplateType;

  @Column({
    type: 'enum',
    enum: TemplateStatus,
    default: TemplateStatus.DRAFT
  })
  status: TemplateStatus;

  @Column({ type: 'json' })
  graph: any;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @Column({ name: 'preview_image', nullable: true })
  previewImage: string;

  @Column({ type: 'simple-array', nullable: true })
  screenshots: string[];

  @Column({ name: 'created_by' })
  createdBy: string;

  @Column({ name: 'created_by_name', length: 100 })
  createdByName: string;

  @Column({ name: 'download_count', default: 0 })
  downloadCount: number;

  @Column({ name: 'like_count', default: 0 })
  likeCount: number;

  @Column({ name: 'view_count', default: 0 })
  viewCount: number;

  @Column({ name: 'rating_average', type: 'float', default: 0 })
  ratingAverage: number;

  @Column({ name: 'rating_count', default: 0 })
  ratingCount: number;

  @Column({ default: false })
  featured: boolean;

  @Column({ name: 'difficulty_level', default: 1 })
  difficultyLevel: number;

  @Column({ name: 'estimated_time_minutes', default: 0 })
  estimatedTimeMinutes: number;

  @Column({ type: 'simple-array', nullable: true })
  tags: string[];

  @Column({ type: 'simple-array', nullable: true })
  requirements: string[];

  @Column({ type: 'text', nullable: true })
  instructions: string;

  @Column({ name: 'version', length: 50, default: '1.0.0' })
  version: string;

  @Column({ name: 'min_engine_version', length: 50, nullable: true })
  minEngineVersion: string;

  @Column({ name: 'file_size', default: 0 })
  fileSize: number;

  @Column({ name: 'node_count', default: 0 })
  nodeCount: number;

  @Column({ name: 'connection_count', default: 0 })
  connectionCount: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 计算属性
  get isDraft(): boolean {
    return this.status === TemplateStatus.DRAFT;
  }

  get isPublished(): boolean {
    return this.status === TemplateStatus.PUBLISHED;
  }

  get isFeatured(): boolean {
    return this.status === TemplateStatus.FEATURED;
  }

  get isDeprecated(): boolean {
    return this.status === TemplateStatus.DEPRECATED;
  }

  get isArchived(): boolean {
    return this.status === TemplateStatus.ARCHIVED;
  }

  get isAvailable(): boolean {
    return this.isPublished || this.isFeatured;
  }

  get popularityScore(): number {
    return (this.downloadCount * 2) + (this.likeCount * 3) + (this.viewCount * 0.1) + (this.ratingAverage * this.ratingCount * 5);
  }

  get difficultyLabel(): string {
    const labels = ['初级', '中级', '高级', '专家', '大师'];
    return labels[Math.min(this.difficultyLevel - 1, labels.length - 1)] || '未知';
  }

  // 辅助方法
  publish(): void {
    this.status = TemplateStatus.PUBLISHED;
    this.updatedAt = new Date();
  }

  feature(): void {
    this.status = TemplateStatus.FEATURED;
    this.featured = true;
    this.updatedAt = new Date();
  }

  unfeature(): void {
    if (this.isFeatured) {
      this.status = TemplateStatus.PUBLISHED;
    }
    this.featured = false;
    this.updatedAt = new Date();
  }

  deprecate(): void {
    this.status = TemplateStatus.DEPRECATED;
    this.updatedAt = new Date();
  }

  archive(): void {
    this.status = TemplateStatus.ARCHIVED;
    this.updatedAt = new Date();
  }

  incrementDownloadCount(): void {
    this.downloadCount++;
  }

  incrementLikeCount(): void {
    this.likeCount++;
  }

  decrementLikeCount(): void {
    if (this.likeCount > 0) {
      this.likeCount--;
    }
  }

  incrementViewCount(): void {
    this.viewCount++;
  }

  updateRating(newRating: number): void {
    const totalRating = this.ratingAverage * this.ratingCount + newRating;
    this.ratingCount++;
    this.ratingAverage = totalRating / this.ratingCount;
  }

  updateStats(nodeCount: number, connectionCount: number, fileSize: number): void {
    this.nodeCount = nodeCount;
    this.connectionCount = connectionCount;
    this.fileSize = fileSize;
    this.updatedAt = new Date();
  }

  addTag(tag: string): void {
    if (!this.tags) {
      this.tags = [];
    }
    if (!this.tags.includes(tag)) {
      this.tags.push(tag);
    }
  }

  removeTag(tag: string): void {
    if (this.tags) {
      this.tags = this.tags.filter(t => t !== tag);
    }
  }

  hasTag(tag: string): boolean {
    return this.tags ? this.tags.includes(tag) : false;
  }

  addRequirement(requirement: string): void {
    if (!this.requirements) {
      this.requirements = [];
    }
    if (!this.requirements.includes(requirement)) {
      this.requirements.push(requirement);
    }
  }

  removeRequirement(requirement: string): void {
    if (this.requirements) {
      this.requirements = this.requirements.filter(r => r !== requirement);
    }
  }

  hasRequirement(requirement: string): boolean {
    return this.requirements ? this.requirements.includes(requirement) : false;
  }

  addScreenshot(url: string): void {
    if (!this.screenshots) {
      this.screenshots = [];
    }
    if (!this.screenshots.includes(url)) {
      this.screenshots.push(url);
    }
  }

  removeScreenshot(url: string): void {
    if (this.screenshots) {
      this.screenshots = this.screenshots.filter(s => s !== url);
    }
  }

  toJSON(): any {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      category: this.category,
      type: this.type,
      status: this.status,
      graph: this.graph,
      metadata: this.metadata,
      previewImage: this.previewImage,
      screenshots: this.screenshots,
      createdBy: this.createdBy,
      createdByName: this.createdByName,
      downloadCount: this.downloadCount,
      likeCount: this.likeCount,
      viewCount: this.viewCount,
      ratingAverage: this.ratingAverage,
      ratingCount: this.ratingCount,
      featured: this.featured,
      difficultyLevel: this.difficultyLevel,
      difficultyLabel: this.difficultyLabel,
      estimatedTimeMinutes: this.estimatedTimeMinutes,
      tags: this.tags,
      requirements: this.requirements,
      instructions: this.instructions,
      version: this.version,
      minEngineVersion: this.minEngineVersion,
      fileSize: this.fileSize,
      nodeCount: this.nodeCount,
      connectionCount: this.connectionCount,
      popularityScore: this.popularityScore,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}
