/**
 * script-collaborator.entity.ts
 * 
 * 脚本协作者实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique
} from 'typeorm';
import { VisualScript } from './visual-script.entity';

/**
 * 协作者角色枚举
 */
export enum CollaboratorRole {
  OWNER = 'owner',
  EDITOR = 'editor',
  VIEWER = 'viewer',
  COMMENTER = 'commenter'
}

/**
 * 协作者状态枚举
 */
export enum CollaboratorStatus {
  ACTIVE = 'active',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
  REMOVED = 'removed'
}

/**
 * 脚本协作者实体
 */
@Entity('script_collaborators')
@Unique(['scriptId', 'userId'])
@Index(['scriptId', 'role'])
@Index(['userId', 'status'])
@Index(['createdAt'])
export class ScriptCollaborator {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'script_id' })
  @Index()
  scriptId: string;

  @Column({ name: 'user_id' })
  @Index()
  userId: string;

  @Column({ name: 'user_name', length: 100 })
  userName: string;

  @Column({ name: 'user_email', length: 255, nullable: true })
  userEmail: string;

  @Column({
    type: 'enum',
    enum: CollaboratorRole,
    default: CollaboratorRole.VIEWER
  })
  role: CollaboratorRole;

  @Column({
    type: 'enum',
    enum: CollaboratorStatus,
    default: CollaboratorStatus.PENDING
  })
  status: CollaboratorStatus;

  @Column({ name: 'invited_by' })
  invitedBy: string;

  @Column({ name: 'invited_by_name', length: 100 })
  invitedByName: string;

  @Column({ name: 'invited_at' })
  invitedAt: Date;

  @Column({ name: 'accepted_at', nullable: true })
  acceptedAt: Date;

  @Column({ name: 'last_active_at', nullable: true })
  lastActiveAt: Date;

  @Column({ name: 'permissions', type: 'json', nullable: true })
  permissions: any;

  @Column({ name: 'access_count', default: 0 })
  accessCount: number;

  @Column({ name: 'edit_count', default: 0 })
  editCount: number;

  @Column({ name: 'comment_count', default: 0 })
  commentCount: number;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => VisualScript, script => script.collaborators, {
    onDelete: 'CASCADE'
  })
  @JoinColumn({ name: 'script_id' })
  script: VisualScript;

  // 计算属性
  get isOwner(): boolean {
    return this.role === CollaboratorRole.OWNER;
  }

  get isEditor(): boolean {
    return this.role === CollaboratorRole.EDITOR;
  }

  get isViewer(): boolean {
    return this.role === CollaboratorRole.VIEWER;
  }

  get isCommenter(): boolean {
    return this.role === CollaboratorRole.COMMENTER;
  }

  get isActive(): boolean {
    return this.status === CollaboratorStatus.ACTIVE;
  }

  get isPending(): boolean {
    return this.status === CollaboratorStatus.PENDING;
  }

  get isSuspended(): boolean {
    return this.status === CollaboratorStatus.SUSPENDED;
  }

  get isRemoved(): boolean {
    return this.status === CollaboratorStatus.REMOVED;
  }

  get canEdit(): boolean {
    return this.isActive && (this.isOwner || this.isEditor);
  }

  get canView(): boolean {
    return this.isActive && (this.isOwner || this.isEditor || this.isViewer || this.isCommenter);
  }

  get canComment(): boolean {
    return this.isActive && (this.isOwner || this.isEditor || this.isCommenter);
  }

  get canManage(): boolean {
    return this.isActive && this.isOwner;
  }

  // 辅助方法
  accept(): void {
    this.status = CollaboratorStatus.ACTIVE;
    this.acceptedAt = new Date();
  }

  suspend(): void {
    this.status = CollaboratorStatus.SUSPENDED;
  }

  remove(): void {
    this.status = CollaboratorStatus.REMOVED;
  }

  activate(): void {
    this.status = CollaboratorStatus.ACTIVE;
  }

  updateRole(role: CollaboratorRole): void {
    this.role = role;
    this.updatedAt = new Date();
  }

  recordAccess(): void {
    this.accessCount++;
    this.lastActiveAt = new Date();
  }

  recordEdit(): void {
    this.editCount++;
    this.lastActiveAt = new Date();
  }

  recordComment(): void {
    this.commentCount++;
    this.lastActiveAt = new Date();
  }

  updatePermissions(permissions: any): void {
    this.permissions = permissions;
    this.updatedAt = new Date();
  }

  hasPermission(permission: string): boolean {
    if (!this.permissions) return false;
    return this.permissions[permission] === true;
  }

  grantPermission(permission: string): void {
    if (!this.permissions) {
      this.permissions = {};
    }
    this.permissions[permission] = true;
    this.updatedAt = new Date();
  }

  revokePermission(permission: string): void {
    if (this.permissions && this.permissions[permission]) {
      delete this.permissions[permission];
      this.updatedAt = new Date();
    }
  }

  /**
   * 检查是否有指定的角色权限
   * @param requiredRole 需要的角色
   * @returns 是否有权限
   */
  hasRolePermission(requiredRole: CollaboratorRole): boolean {
    const roleHierarchy = {
      [CollaboratorRole.OWNER]: 4,
      [CollaboratorRole.EDITOR]: 3,
      [CollaboratorRole.COMMENTER]: 2,
      [CollaboratorRole.VIEWER]: 1
    };

    return roleHierarchy[this.role] >= roleHierarchy[requiredRole];
  }

  /**
   * 获取角色显示名称
   * @returns 角色显示名称
   */
  getRoleDisplayName(): string {
    const roleNames = {
      [CollaboratorRole.OWNER]: '所有者',
      [CollaboratorRole.EDITOR]: '编辑者',
      [CollaboratorRole.COMMENTER]: '评论者',
      [CollaboratorRole.VIEWER]: '查看者'
    };

    return roleNames[this.role] || '未知';
  }

  /**
   * 获取状态显示名称
   * @returns 状态显示名称
   */
  getStatusDisplayName(): string {
    const statusNames = {
      [CollaboratorStatus.ACTIVE]: '活跃',
      [CollaboratorStatus.PENDING]: '待接受',
      [CollaboratorStatus.SUSPENDED]: '已暂停',
      [CollaboratorStatus.REMOVED]: '已移除'
    };

    return statusNames[this.status] || '未知';
  }

  toJSON(): any {
    return {
      id: this.id,
      scriptId: this.scriptId,
      userId: this.userId,
      userName: this.userName,
      userEmail: this.userEmail,
      role: this.role,
      roleDisplayName: this.getRoleDisplayName(),
      status: this.status,
      statusDisplayName: this.getStatusDisplayName(),
      invitedBy: this.invitedBy,
      invitedByName: this.invitedByName,
      invitedAt: this.invitedAt,
      acceptedAt: this.acceptedAt,
      lastActiveAt: this.lastActiveAt,
      permissions: this.permissions,
      accessCount: this.accessCount,
      editCount: this.editCount,
      commentCount: this.commentCount,
      notes: this.notes,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}
